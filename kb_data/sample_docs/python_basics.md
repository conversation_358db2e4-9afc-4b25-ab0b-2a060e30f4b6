# Python Programming Basics

## Introduction to Python

Python is a high-level, interpreted programming language known for its simplicity and readability. It was created by <PERSON> and first released in 1991.

## Key Features

### Easy to Learn and Use
Python has a simple syntax that closely resembles natural language, making it an excellent choice for beginners.

### Interpreted Language
Python code is executed line by line, which makes debugging easier and development faster.

### Object-Oriented Programming
Python supports object-oriented programming paradigms, allowing you to create classes and objects.

## Basic Syntax

### Variables and Data Types

```python
# Numbers
age = 25
price = 19.99

# Strings
name = "Alice"
message = 'Hello, <PERSON>!'

# Booleans
is_student = True
is_working = False

# Lists
fruits = ["apple", "banana", "orange"]
numbers = [1, 2, 3, 4, 5]
```

### Control Structures

#### If Statements
```python
if age >= 18:
    print("You are an adult")
elif age >= 13:
    print("You are a teenager")
else:
    print("You are a child")
```

#### Loops
```python
# For loop
for fruit in fruits:
    print(fruit)

# While loop
count = 0
while count < 5:
    print(count)
    count += 1
```

### Functions

```python
def greet(name):
    return f"Hello, {name}!"

def add_numbers(a, b):
    return a + b

# Function calls
message = greet("<PERSON>")
result = add_numbers(5, 3)
```

## Common Built-in Functions

- `print()` - Display output
- `len()` - Get length of objects
- `type()` - Get type of object
- `input()` - Get user input
- `range()` - Generate sequence of numbers

## Best Practices

1. Use meaningful variable names
2. Follow PEP 8 style guidelines
3. Write comments to explain complex logic
4. Use functions to organize code
5. Handle exceptions properly

## Conclusion

Python is an excellent language for beginners and professionals alike. Its simplicity and powerful libraries make it suitable for web development, data science, automation, and many other applications.

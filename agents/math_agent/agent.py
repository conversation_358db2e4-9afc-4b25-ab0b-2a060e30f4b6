"""
@module agents.math_agent.agent
@description Math agent for basic mathematical operations and calculations
"""

import re
import math
from typing import Dict, Any
from utils.logging_config import get_logger

logger = get_logger("agents.math_agent")

def load_config() -> Dict[str, Any]:
    """Load agent configuration"""
    return {
        "name": "Math Agent",
        "description": "A mathematical agent that can perform calculations and solve basic math problems",
        "version": "1.0.0",
        "capabilities": [
            "Basic arithmetic (+, -, *, /)",
            "Mathematical functions (sin, cos, tan, log, sqrt)",
            "Constants (pi, e)",
            "Expression evaluation"
        ],
        "parameters": {
            "precision": 6,
            "angle_unit": "radians"
        }
    }

def run(message: str, context: Dict[str, Any] = None) -> str:
    """
    Process mathematical queries and return results
    
    Args:
        message: User input containing mathematical expression or question
        context: Additional context (unused in this agent)
        
    Returns:
        Mathematical result or explanation
    """
    logger.info(f"Math agent processing: {message[:50]}...")
    
    try:
        # Clean the message
        message = message.strip()
        
        # Handle help requests
        if any(word in message.lower() for word in ['help', 'what can you do', 'capabilities']):
            return get_help_text()
        
        # Try to extract and evaluate mathematical expressions
        result = process_math_query(message)
        
        logger.info("Math agent completed calculation")
        return result
        
    except Exception as e:
        logger.error(f"Error in math agent: {str(e)}")
        return f"Sorry, I couldn't process that mathematical expression. Error: {str(e)}\n\nTry something like: '2 + 2' or 'sqrt(16)' or ask for 'help'"

def process_math_query(query: str) -> str:
    """Process a mathematical query"""
    
    # Remove common words and phrases
    math_query = query.lower()
    for phrase in ['calculate', 'what is', 'solve', 'compute', 'find']:
        math_query = math_query.replace(phrase, '')
    
    math_query = math_query.strip()
    
    # Handle special cases
    if 'pi' in math_query:
        math_query = math_query.replace('pi', str(math.pi))
    if 'e' in math_query and not re.search(r'[a-df-z]', math_query.replace('e', '')):
        math_query = math_query.replace('e', str(math.e))
    
    # Replace common mathematical functions
    replacements = {
        'sqrt': 'math.sqrt',
        'sin': 'math.sin',
        'cos': 'math.cos',
        'tan': 'math.tan',
        'log': 'math.log',
        'ln': 'math.log',
        'exp': 'math.exp',
        'abs': 'abs',
        'pow': 'pow',
        '^': '**'
    }
    
    for old, new in replacements.items():
        math_query = re.sub(r'\b' + old + r'\b', new, math_query)
    
    try:
        # Evaluate the expression safely
        # Note: In production, you'd want more robust expression parsing
        allowed_names = {
            "math": math,
            "abs": abs,
            "pow": pow,
            "round": round,
            "min": min,
            "max": max
        }
        
        # Only allow safe mathematical operations
        if re.search(r'[a-zA-Z_][a-zA-Z0-9_]*\s*\(', math_query):
            # Contains function calls - evaluate with restricted namespace
            result = eval(math_query, {"__builtins__": {}}, allowed_names)
        else:
            # Simple arithmetic - safe to evaluate
            result = eval(math_query)
        
        # Format the result nicely
        if isinstance(result, float):
            if result.is_integer():
                result = int(result)
            else:
                result = round(result, 6)
        
        return f"📊 **Result:** {result}\n\n*Calculation:* `{query}` = `{result}`"
        
    except ZeroDivisionError:
        return "❌ **Error:** Division by zero is not allowed!"
    except ValueError as e:
        return f"❌ **Error:** Invalid mathematical operation - {str(e)}"
    except SyntaxError:
        return f"❌ **Error:** Invalid mathematical expression. Please check your syntax.\n\nExample: `2 + 3 * 4` or `sqrt(25)`"
    except Exception as e:
        return f"❌ **Error:** Could not evaluate expression - {str(e)}"

def get_help_text() -> str:
    """Return help text for the math agent"""
    return """🧮 **Math Agent Help**

I can help you with mathematical calculations! Here's what I can do:

**Basic Operations:**
- Addition: `2 + 3`
- Subtraction: `10 - 4`
- Multiplication: `5 * 6`
- Division: `15 / 3`
- Exponentiation: `2^3` or `2**3`

**Mathematical Functions:**
- Square root: `sqrt(16)`
- Trigonometry: `sin(pi/2)`, `cos(0)`, `tan(pi/4)`
- Logarithms: `log(10)`, `ln(e)`
- Absolute value: `abs(-5)`

**Constants:**
- Pi: `pi` (≈ 3.14159)
- Euler's number: `e` (≈ 2.71828)

**Examples:**
- "What is 2 + 2?"
- "Calculate sqrt(144)"
- "Find sin(pi/2)"
- "Solve 3 * 4 + 2"

Just type your mathematical expression and I'll calculate it for you! 🚀"""

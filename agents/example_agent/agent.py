"""
@module agents.example_agent.agent
@description Example agent implementation demonstrating the agent interface
"""

import asyncio
from typing import Dict, Any, AsyncGenerator
from utils.logging_config import get_logger

logger = get_logger("agents.example_agent")

def load_config() -> Dict[str, Any]:
    """Load agent configuration"""
    return {
        "name": "Example Agent",
        "description": "A simple example agent that demonstrates basic functionality",
        "version": "1.0.0",
        "capabilities": [
            "Text processing",
            "Simple conversations",
            "Echo responses"
        ],
        "parameters": {
            "response_delay": 0.1,
            "max_response_length": 1000
        }
    }

async def run(message: str, context: Dict[str, Any] = None) -> AsyncGenerator[str, None]:
    """
    Main agent execution function
    
    Args:
        message: User input message
        context: Additional context information
        
    Yields:
        Response chunks for streaming
    """
    logger.info(f"Example agent processing message: {message[:50]}...")
    
    if context is None:
        context = {}
    
    try:
        # Simulate processing delay
        await asyncio.sleep(0.1)
        
        # Simple response logic based on message content
        if "hello" in message.lower():
            response = await handle_greeting(message)
        elif "help" in message.lower():
            response = await handle_help_request()
        elif "echo" in message.lower():
            response = await handle_echo(message)
        elif "time" in message.lower():
            response = await handle_time_request()
        else:
            response = await handle_general_message(message)
        
        # Stream the response word by word
        words = response.split()
        for i, word in enumerate(words):
            yield word + (" " if i < len(words) - 1 else "")
            await asyncio.sleep(0.05)  # Small delay for streaming effect
            
        logger.info("Example agent completed processing")
        
    except Exception as e:
        logger.error(f"Error in example agent: {str(e)}")
        yield f"Sorry, I encountered an error: {str(e)}"

async def handle_greeting(message: str) -> str:
    """Handle greeting messages"""
    greetings = [
        "Hello! I'm the Example Agent. How can I help you today?",
        "Hi there! Nice to meet you. What would you like to talk about?",
        "Greetings! I'm here to assist you with various tasks."
    ]
    
    # Simple selection based on message length
    index = len(message) % len(greetings)
    return greetings[index]

async def handle_help_request() -> str:
    """Handle help requests"""
    return """I'm the Example Agent and I can help you with:

• **Greetings**: Say hello and I'll greet you back
• **Echo**: Ask me to echo something and I'll repeat it
• **Time**: Ask about the time and I'll tell you it's always time to code!
• **General chat**: I can respond to general messages

Try saying things like:
- "Hello there!"
- "Echo: This is a test"
- "What time is it?"
- "Help me understand something"

I'm just an example, but I demonstrate how agents work in this system!"""

async def handle_echo(message: str) -> str:
    """Handle echo requests"""
    # Extract the part after "echo"
    echo_part = message.lower().split("echo", 1)
    if len(echo_part) > 1:
        text_to_echo = echo_part[1].strip(": ")
        return f"🔊 Echo: {text_to_echo}"
    else:
        return "🔊 Echo: (nothing to echo)"

async def handle_time_request() -> str:
    """Handle time-related requests"""
    import datetime
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    return f"⏰ The current time is {current_time}. But remember, it's always time to code! 💻"

async def handle_general_message(message: str) -> str:
    """Handle general messages"""
    responses = [
        f"That's interesting! You said: '{message}'. I'm just an example agent, but I'm listening!",
        f"I hear you talking about '{message}'. As an example agent, I find that fascinating!",
        f"Thanks for sharing: '{message}'. I'm designed to show how agents work in this system.",
        f"You mentioned: '{message}'. I'm a simple example, but I try to be helpful!",
        f"Regarding '{message}' - I'm just an example agent, but I appreciate the conversation!"
    ]
    
    # Select response based on message length
    index = len(message) % len(responses)
    return responses[index]

# Alternative synchronous run method (for demonstration)
def run_sync(message: str, context: Dict[str, Any] = None) -> str:
    """
    Synchronous version of the run method
    This shows how agents can also work synchronously
    """
    logger.info(f"Example agent (sync) processing: {message[:50]}...")
    
    if "sync" in message.lower():
        return f"This is a synchronous response to: {message}"
    else:
        return "I'm running in synchronous mode. Try including 'sync' in your message!"

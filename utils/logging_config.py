"""
@module utils.logging_config
@description Centralized logging configuration for AgentsUI application
"""

import logging
import logging.handlers
import os
from pathlib import Path
from config import settings

def setup_logging():
    """Configure logging for the application with multiple handlers"""
    
    # Ensure logs directory exists
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)
    
    # Create formatters
    detailed_formatter = logging.Formatter(
        '%(asctime)s [%(levelname)s] %(name)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, settings.log_level.upper()))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(detailed_formatter)
    root_logger.addHandler(console_handler)
    
    # Main app log file
    app_handler = logging.handlers.RotatingFileHandler(
        "logs/app.log",
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    app_handler.setLevel(logging.INFO)
    app_handler.setFormatter(detailed_formatter)
    root_logger.addHandler(app_handler)
    
    # Error log file
    error_handler = logging.handlers.RotatingFileHandler(
        "logs/error.log",
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(detailed_formatter)
    root_logger.addHandler(error_handler)
    
    # Debug log file (only in debug mode)
    if settings.debug:
        debug_handler = logging.handlers.RotatingFileHandler(
            "logs/dev_debug.log",
            maxBytes=10*1024*1024,  # 10MB
            backupCount=3
        )
        debug_handler.setLevel(logging.DEBUG)
        debug_handler.setFormatter(detailed_formatter)
        root_logger.addHandler(debug_handler)
    
    # Module-specific loggers
    setup_module_loggers()

def setup_module_loggers():
    """Setup module-specific loggers with their own log files"""
    
    modules = [
        ("agents", "logs/agents.log"),
        ("chat", "logs/chat.log"),
        ("kb", "logs/kb.log")
    ]
    
    detailed_formatter = logging.Formatter(
        '%(asctime)s [%(levelname)s] %(name)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    for module_name, log_file in modules:
        logger = logging.getLogger(module_name)
        
        # Module-specific file handler
        handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=5*1024*1024,  # 5MB
            backupCount=3
        )
        handler.setLevel(logging.INFO)
        handler.setFormatter(detailed_formatter)
        logger.addHandler(handler)
        
        # Prevent duplicate logs in root logger
        logger.propagate = True

def get_logger(name: str) -> logging.Logger:
    """Get a logger instance for the given name"""
    return logging.getLogger(name)



* * *

AgentsUI MVP 项目需求与开发维护规范文档
==========================

> 版本：v2.3  
> 状态：已设计  
> 维护者：技术组 & AI  
> 目标：构建一个结构清晰、易维护、AI友好、功能完整的多Agent人机交互平台

* * *

目录
--

1. [项目简介](https://chatgpt.com/c/68904a73-0ea4-8002-a859-acb2210cf8bc#1-%E9%A1%B9%E7%9B%AE%E7%AE%80%E4%BB%8B)

2. [技术架构与选型](https://chatgpt.com/c/68904a73-0ea4-8002-a859-acb2210cf8bc#2-%E6%8A%80%E6%9C%AF%E6%9E%B6%E6%9E%84%E4%B8%8E%E9%80%89%E5%9E%8B)

3. [系统功能模块](https://chatgpt.com/c/68904a73-0ea4-8002-a859-acb2210cf8bc#3-%E7%B3%BB%E7%BB%9F%E5%8A%9F%E8%83%BD%E6%A8%A1%E5%9D%97)

4. [项目结构建议](https://chatgpt.com/c/68904a73-0ea4-8002-a859-acb2210cf8bc#4-%E9%A1%B9%E7%9B%AE%E7%BB%93%E6%9E%84%E5%BB%BA%E8%AE%AE)

5. [AI友好开发与维护约定](https://chatgpt.com/c/68904a73-0ea4-8002-a859-acb2210cf8bc#5-ai%E5%8F%8B%E5%A5%BD%E5%BC%80%E5%8F%91%E4%B8%8E%E7%BB%B4%E6%8A%A4%E7%BA%A6%E5%AE%9A)

6. [日志目录结构与规范](https://chatgpt.com/c/68904a73-0ea4-8002-a859-acb2210cf8bc#6-%E6%97%A5%E5%BF%97%E7%9B%AE%E5%BD%95%E7%BB%93%E6%9E%84%E4%B8%8E%E8%A7%84%E8%8C%83)

7. [测试策略总览](https://chatgpt.com/c/68904a73-0ea4-8002-a859-acb2210cf8bc#7-%E6%B5%8B%E8%AF%95%E7%AD%96%E7%95%A5%E6%80%BB%E8%A7%88)

8. [单元测试规范](https://chatgpt.com/c/68904a73-0ea4-8002-a859-acb2210cf8bc#8-%E5%8D%95%E5%85%83%E6%B5%8B%E8%AF%95%E8%A7%84%E8%8C%83)

9. [系统测试规范（端到端）](https://chatgpt.com/c/68904a73-0ea4-8002-a859-acb2210cf8bc#9-%E7%B3%BB%E7%BB%9F%E6%B5%8B%E8%AF%95%E8%A7%84%E8%8C%83%E7%AB%AF%E5%88%B0%E7%AB%AF)

10. [测试编写流程规范](https://chatgpt.com/c/68904a73-0ea4-8002-a859-acb2210cf8bc#10-%E6%B5%8B%E8%AF%95%E7%BC%96%E5%86%99%E6%B5%81%E7%A8%8B%E8%A7%84%E8%8C%83)

11. [AI结构元信息定义](https://chatgpt.com/c/68904a73-0ea4-8002-a859-acb2210cf8bc#11-ai%E7%BB%93%E6%9E%84%E5%85%83%E4%BF%A1%E6%81%AF%E5%AE%9A%E4%B9%89)

* * *

1. 项目简介

-------

AgentsUI 是一个面向本地部署的多Agent管理与对话平台，目标是：

* 快速加载、卸载、运行本地Agent

* 支持基于Markdown的文本对话与知识库支持

* 一体化部署，极简UI，AI辅助可维护

* * *

2. 技术架构与选型

----------

### 后端

| 模块      | 技术        |
| ------- | --------- |
| 框架      | FastAPI   |
| Agent编排 | LangChain |
| 向量存储    | ChromaDB  |
| 文件上传    | Python标准库 |
| 通信方式    | SSE       |
| 认证      | JWT（单用户）  |

### 前端

| 模块          | 技术                    |
| ----------- | --------------------- |
| UI 框架       | Pico.css              |
| JS          | 原生 JavaScript + fetch |
| Markdown 渲染 | Marked.js             |

### 部署

* 采用单体结构，FastAPI 同时托管前端

* 使用 `uvicorn` 启动即可运行

* * *

3. 系统功能模块

---------

### 3.1 Agent 管理

* 自动发现 `agents/` 目录中的Agent

* 支持加载、卸载Agent

* 返回运行状态和配置说明

### 3.2 知识库管理

* 新建命名空间

* 上传 `.md` / `.txt` 文件

* 文档切分并嵌入向量数据库

### 3.3 对话交互

* 列出已加载Agent

* 点击进入聊天

* SSE流式响应，Markdown渲染

* * *

4. 项目结构建议

---------

    AgentsUI/
    ├── main.py                 # 项目入口
    ├── routers/                # 所有API路由模块
    │   ├── agents.py
    │   ├── chat.py
    │   └── kb.py
    ├── agents/                 # 每个Agent一个目录
    │   └── example_agent/
    │       └── agent.py
    ├── static/                 # 前端HTML+CSS+JS
    │   └── ...
    ├── utils/                  # 日志、工具类
    │   └── logging_config.py
    ├── kb_data/                # 知识库文档数据
    ├── logs/                   # 日志目录（自动生成）
    ├── tests/                  # 单元 & 系统测试
    ├── config.py               # 配置加载
    ├── .env                    # 环境变量
    ├── requirements.txt
    └── project_meta.json       # AI结构识别元数据

* * *

5. AI友好开发与维护约定

--------------

| 项       | 说明                                            |
| ------- | --------------------------------------------- |
| 文件命名    | 小写+下划线，如 `agent_loader.py`                    |
| 函数命名    | 动宾结构，如 `load_agent()`                         |
| 类命名     | PascalCase                                    |
| Agent接口 | 每个 `agent.py` 实现 `run()` 与 `load_config()`    |
| API路径   | `/api/<模块>/<功能>`，如 `/api/agents/load`         |
| SSE响应   | `{ type: "chunk", data: "..." }`              |
| 前端组件    | 所有交互元素使用 `data-component` 标记                  |
| 日志嵌入    | 每个函数至少包含一个 `logger.info` 和异常路径 `logger.error` |
| AI提示注释  | 每个文件顶部用 `@module`、`@description` 注释供AI读取      |

* * *

6. 日志目录结构与规范

------------

    logs/
    ├── app.log         # 总日志（默认）
    ├── agents.log      # Agent模块日志
    ├── chat.log        # 对话模块日志
    ├── kb.log          # 知识库模块日志
    ├── error.log       # 所有错误级别日志
    ├── dev_debug.log   # DEBUG专用

日志格式：
    2025-08-04 13:35:21 [INFO] agents.loader - Loaded agent: example_agent

配置模块：`utils/logging_config.py`，统一加载格式、大小、切分等。

* * *

7. 测试策略总览

---------

| 类型       | 工具                     | 用途      |
| -------- | ---------------------- | ------- |
| 单元测试     | pytest                 | 函数级测试   |
| 系统测试     | httpx + pytest-asyncio | API接口验证 |
| UI测试（可选） | Playwright / Selenium  | 浏览器行为测试 |

所有测试应自动覆盖：边界输入、错误处理、响应格式。

* * *

8. 单元测试规范

---------

* 所有模块的逻辑函数需单独测试文件

* 命名规则：`test_<模块名>.py`

* 函数命名：`test_<函数名>_<行为>()`

* 示例：

    def test_list_agents_returns_list():
        from agents.agent_loader import list_agents
        assert isinstance(list_agents(), list)

* * *

9. 系统测试规范（端到端）

--------------

* 用 `AsyncClient(app=app)` 模拟请求

* 重点测试路由、响应结构、状态码

* 示例：

    @pytest.mark.asyncio
    async def test_chat_send():
        async with AsyncClient(app=app, base_url="http://test") as c:
            res = await c.post("/api/chat/example/send", json={"message": "hi"})
            assert res.status_code == 200

* * *

10. 测试编写流程规范

------------

1. 明确被测函数或API

2. 在 `tests/` 新建测试文件

3. 覆盖常规输入、空输入、异常输入

4. 运行 `pytest` 验证

5. 为每个新API、Agent都补充对应测试用例

> AI提示语建议：
> 
> * “为 `load_agent(name)` 写一个pytest单元测试，要求测试正常与Agent不存在的情况。”

* * *

11. AI结构元信息定义

-------------

### 文件：`project_meta.json`

    {
      "name": "AgentsUI",
      "version": "0.1.0",
      "structure": "modular",
      "agents_path": "agents/",
      "knowledge_base_path": "kb_data/",
      "frontend_path": "static/"
    }

> 说明：该结构供 AI 自动识别项目组织、路径规则与模块名称，用于 AI 自动生成模块、测试、Agent插件等。

* * *

✅ 附录：运行命令参考
-----------

    # 安装依赖
    pip install -r requirements.txt
    
    # 启动服务
    python main.py
    
    # 运行测试
    pytest tests/

* * *

"""
@module main
@description Main entry point for AgentsUI application
"""

import uvicorn
from fastapi import FastAP<PERSON>
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from pathlib import Path

from config import settings
from utils.logging_config import setup_logging, get_logger
from routers import agents, chat, kb

# Setup logging
setup_logging()
logger = get_logger(__name__)

# Create FastAPI app
app = FastAPI(
    title="AgentsUI",
    description="Multi-Agent Management and Chat Platform",
    version="0.1.0",
    debug=settings.debug
)

# Include API routers
app.include_router(agents.router, prefix="/api/agents", tags=["agents"])
app.include_router(chat.router, prefix="/api/chat", tags=["chat"])
app.include_router(kb.router, prefix="/api/kb", tags=["knowledge-base"])

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

@app.get("/")
async def root():
    """Serve the main HTML page"""
    logger.info("Serving main page")
    return FileResponse("static/index.html")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    logger.info("Health check requested")
    return {"status": "healthy", "version": "0.1.0"}

@app.on_event("startup")
async def startup_event():
    """Application startup event"""
    logger.info("Starting AgentsUI application")
    logger.info(f"Debug mode: {settings.debug}")
    logger.info(f"Agents path: {settings.agents_path}")
    logger.info(f"Knowledge base path: {settings.kb_data_path}")

@app.on_event("shutdown")
async def shutdown_event():
    """Application shutdown event"""
    logger.info("Shutting down AgentsUI application")

if __name__ == "__main__":
    logger.info(f"Starting server on {settings.host}:{settings.port}")
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )

"""
@module tests.test_agents
@description Unit tests for agent management functionality
"""

import pytest
from pathlib import Path
import importlib.util
from routers.agents import list_agents, load_agent, unload_agent, get_agent_config, loaded_agents

@pytest.mark.asyncio
async def test_list_agents_returns_list(temp_agents_dir):
    """Test that list_agents returns a list"""
    from routers.agents import list_agents
    result = await list_agents()
    assert isinstance(result, list)

@pytest.mark.asyncio
async def test_list_agents_empty_directory(temp_agents_dir):
    """Test list_agents with empty agents directory"""
    # The temp_agents_dir fixture creates an empty directory
    from routers.agents import list_agents
    agents = await list_agents()
    assert len(agents) == 0

@pytest.mark.asyncio
async def test_list_agents_with_sample_agent(sample_agent_dir):
    """Test list_agents with a sample agent"""
    from routers.agents import list_agents
    agents = await list_agents()
    assert len(agents) == 1
    assert agents[0]["name"] == "test_agent"
    assert agents[0]["loaded"] == False

@pytest.mark.asyncio
async def test_load_agent_success(sample_agent_dir):
    """Test successful agent loading"""
    agent_name = "test_agent"
    
    # Initially agent should not be loaded
    assert agent_name not in loaded_agents
    
    # Load the agent
    result = await load_agent(agent_name)
    
    assert result["status"] == "loaded"
    assert result["agent"] == agent_name
    assert agent_name in loaded_agents

@pytest.mark.asyncio
async def test_load_agent_not_found(temp_agents_dir):
    """Test loading non-existent agent"""
    with pytest.raises(Exception):  # Should raise HTTPException
        await load_agent("nonexistent_agent")

@pytest.mark.asyncio
async def test_load_agent_already_loaded(sample_agent_dir):
    """Test loading already loaded agent"""
    agent_name = "test_agent"
    
    # Load the agent first time
    await load_agent(agent_name)
    
    # Try to load again
    result = await load_agent(agent_name)
    assert result["status"] == "already_loaded"

@pytest.mark.asyncio
async def test_unload_agent_success(sample_agent_dir):
    """Test successful agent unloading"""
    agent_name = "test_agent"
    
    # Load the agent first
    await load_agent(agent_name)
    assert agent_name in loaded_agents
    
    # Unload the agent
    result = await unload_agent(agent_name)
    
    assert result["status"] == "unloaded"
    assert result["agent"] == agent_name
    assert agent_name not in loaded_agents

@pytest.mark.asyncio
async def test_unload_agent_not_loaded(temp_agents_dir):
    """Test unloading agent that's not loaded"""
    agent_name = "test_agent"
    
    result = await unload_agent(agent_name)
    assert result["status"] == "not_loaded"

@pytest.mark.asyncio
async def test_get_agent_config_success(sample_agent_dir):
    """Test getting agent configuration"""
    agent_name = "test_agent"
    
    config = await get_agent_config(agent_name)
    
    assert isinstance(config, dict)
    assert config["name"] == "Test Agent"
    assert config["description"] == "A test agent for unit testing"
    assert config["version"] == "1.0.0"

@pytest.mark.asyncio
async def test_get_agent_config_not_found(temp_agents_dir):
    """Test getting config for non-existent agent"""
    config = await get_agent_config("nonexistent_agent")
    assert config == {}

def test_agent_interface_compliance(sample_agent_dir):
    """Test that sample agent complies with the agent interface"""
    agent_path = sample_agent_dir / "agent.py"
    
    # Load the agent module
    spec = importlib.util.spec_from_file_location("test_agent", agent_path)
    agent_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(agent_module)
    
    # Check required functions exist
    assert hasattr(agent_module, 'load_config')
    assert hasattr(agent_module, 'run')
    
    # Check load_config returns dict
    config = agent_module.load_config()
    assert isinstance(config, dict)
    
    # Check run function works
    response = agent_module.run("test message")
    assert isinstance(response, str)
    assert "test message" in response

"""
@module tests.conftest
@description Pytest configuration and fixtures for AgentsUI tests
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from fastapi.testclient import TestClient
import sys
import os

# Add the parent directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from main import app
from config import settings

@pytest.fixture
def client():
    """Create a test client for the FastAPI app"""
    return TestClient(app)

@pytest.fixture
def temp_agents_dir():
    """Create a temporary agents directory for testing"""
    temp_dir = tempfile.mkdtemp()
    original_agents_path = settings.agents_path
    settings.agents_path = temp_dir
    
    yield temp_dir
    
    # Cleanup
    settings.agents_path = original_agents_path
    shutil.rmtree(temp_dir)

@pytest.fixture
def temp_kb_dir():
    """Create a temporary knowledge base directory for testing"""
    temp_dir = tempfile.mkdtemp()
    original_kb_path = settings.kb_data_path
    settings.kb_data_path = temp_dir
    
    yield temp_dir
    
    # Cleanup
    settings.kb_data_path = original_kb_path
    shutil.rmtree(temp_dir)

@pytest.fixture
def sample_agent_dir(temp_agents_dir):
    """Create a sample agent for testing"""
    agent_dir = Path(temp_agents_dir) / "test_agent"
    agent_dir.mkdir()
    
    # Create a simple test agent
    agent_code = '''
def load_config():
    return {
        "name": "Test Agent",
        "description": "A test agent for unit testing",
        "version": "1.0.0"
    }

def run(message, context=None):
    return f"Test response to: {message}"
'''
    
    (agent_dir / "agent.py").write_text(agent_code)
    return agent_dir

@pytest.fixture
def sample_markdown_file():
    """Create a sample markdown file for testing"""
    content = """# Test Document

This is a test document for knowledge base testing.

## Section 1

Some content here.

## Section 2

More content here with **bold** and *italic* text.
"""
    return content

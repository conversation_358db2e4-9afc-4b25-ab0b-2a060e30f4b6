"""
@module tests.test_api_system
@description System tests for AgentsUI API endpoints
"""

import pytest
import json
import io
from fastapi.testclient import TestClient

def test_health_endpoint(client):
    """Test the health check endpoint"""
    response = client.get("/health")
    
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert "version" in data

def test_root_endpoint(client):
    """Test the root endpoint serves HTML"""
    response = client.get("/")
    
    # Should redirect to static file or return HTML
    assert response.status_code in [200, 404]  # 404 if static file doesn't exist in test

def test_agents_list_endpoint(client, sample_agent_dir):
    """Test listing agents endpoint"""
    response = client.get("/api/agents/list")
    
    assert response.status_code == 200
    agents = response.json()
    assert isinstance(agents, list)
    
    if len(agents) > 0:
        agent = agents[0]
        assert "name" in agent
        assert "loaded" in agent
        assert "config" in agent

def test_agents_load_endpoint(client, sample_agent_dir):
    """Test loading an agent"""
    agent_name = "test_agent"
    
    response = client.post(f"/api/agents/load/{agent_name}")
    
    assert response.status_code == 200
    data = response.json()
    assert data["status"] in ["loaded", "already_loaded"]
    assert data["agent"] == agent_name

def test_agents_load_nonexistent(client):
    """Test loading non-existent agent"""
    response = client.post("/api/agents/load/nonexistent_agent")
    
    assert response.status_code == 404

def test_agents_unload_endpoint(client, sample_agent_dir):
    """Test unloading an agent"""
    agent_name = "test_agent"
    
    # Load first
    client.post(f"/api/agents/load/{agent_name}")
    
    # Then unload
    response = client.post(f"/api/agents/unload/{agent_name}")
    
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "unloaded"
    assert data["agent"] == agent_name

def test_agents_status_endpoint(client, sample_agent_dir):
    """Test getting agent status"""
    agent_name = "test_agent"
    
    response = client.get(f"/api/agents/status/{agent_name}")
    
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == agent_name
    assert "loaded" in data
    assert "config" in data

def test_chat_send_without_loaded_agent(client):
    """Test sending message to unloaded agent"""
    response = client.post(
        "/api/chat/send/nonexistent_agent",
        json={"message": "hello"}
    )
    
    assert response.status_code == 404

def test_chat_send_with_loaded_agent(client, sample_agent_dir):
    """Test sending message to loaded agent"""
    agent_name = "test_agent"
    
    # Load the agent first
    client.post(f"/api/agents/load/{agent_name}")
    
    # Send message
    response = client.post(
        f"/api/chat/send/{agent_name}",
        json={"message": "hello"}
    )
    
    assert response.status_code == 200
    # Response should be streaming, so we check content type
    assert "text/plain" in response.headers.get("content-type", "")

def test_chat_history_endpoint(client):
    """Test getting chat history"""
    agent_name = "test_agent"
    
    response = client.get(f"/api/chat/history/{agent_name}")
    
    assert response.status_code == 200
    data = response.json()
    assert data["agent"] == agent_name
    assert "history" in data

def test_kb_namespaces_list(client):
    """Test listing knowledge base namespaces"""
    response = client.get("/api/kb/namespaces")
    
    assert response.status_code == 200
    namespaces = response.json()
    assert isinstance(namespaces, list)

def test_kb_create_namespace(client):
    """Test creating a knowledge base namespace"""
    namespace_name = "test_namespace"
    
    response = client.post(f"/api/kb/namespaces/{namespace_name}")
    
    assert response.status_code == 200
    data = response.json()
    assert data["status"] in ["created", "exists"]
    assert data["namespace"] == namespace_name

def test_kb_delete_namespace(client):
    """Test deleting a knowledge base namespace"""
    namespace_name = "test_namespace_delete"
    
    # Create first
    client.post(f"/api/kb/namespaces/{namespace_name}")
    
    # Then delete
    response = client.delete(f"/api/kb/namespaces/{namespace_name}")
    
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "deleted"
    assert data["namespace"] == namespace_name

def test_kb_upload_document(client, sample_markdown_file):
    """Test uploading a document to knowledge base"""
    namespace_name = "test_upload_namespace"
    
    # Create namespace first
    client.post(f"/api/kb/namespaces/{namespace_name}")
    
    # Create file-like object
    file_content = sample_markdown_file.encode('utf-8')
    files = {"file": ("test.md", io.BytesIO(file_content), "text/markdown")}
    data = {"chunk_size": "1000"}
    
    response = client.post(
        f"/api/kb/upload/{namespace_name}",
        files=files,
        data=data
    )
    
    assert response.status_code == 200
    result = response.json()
    assert result["status"] == "uploaded"
    assert result["filename"] == "test.md"
    assert "chunks" in result

def test_kb_search_empty_namespace(client):
    """Test searching in empty namespace"""
    namespace_name = "empty_namespace"
    
    # Create empty namespace
    client.post(f"/api/kb/namespaces/{namespace_name}")
    
    response = client.get(f"/api/kb/search/{namespace_name}?query=test")
    
    assert response.status_code == 200
    data = response.json()
    assert data["query"] == "test"
    assert data["namespace"] == namespace_name
    assert len(data["results"]) == 0

def test_invalid_endpoints(client):
    """Test various invalid endpoints return appropriate errors"""
    # Invalid agent operations
    assert client.get("/api/agents/invalid").status_code == 404
    assert client.post("/api/agents/load/").status_code == 404
    
    # Invalid chat operations
    assert client.post("/api/chat/send/").status_code == 404
    
    # Invalid KB operations
    assert client.get("/api/kb/invalid").status_code == 404

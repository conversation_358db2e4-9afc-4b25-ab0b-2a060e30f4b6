"""
@module tests.test_chat
@description Unit tests for chat functionality
"""

import pytest
import json
from routers.chat import ChatMessage, stream_agent_response
from routers.agents import loaded_agents

class MockAgent:
    """Mock agent for testing"""
    
    def __init__(self, response_type="sync"):
        self.response_type = response_type
    
    def run(self, message, context=None):
        """Synchronous run method"""
        return f"Mock response to: {message}"
    
    async def run_async(self, message, context=None):
        """Asynchronous run method that yields chunks"""
        words = f"Mock async response to: {message}".split()
        for word in words:
            yield word + " "

def test_chat_message_model():
    """Test ChatMessage pydantic model"""
    message = ChatMessage(message="Hello", context={"key": "value"})
    
    assert message.message == "Hello"
    assert message.context == {"key": "value"}

def test_chat_message_model_defaults():
    """Test ChatMessage with default context"""
    message = ChatMessage(message="Hello")
    
    assert message.message == "Hello"
    assert message.context == {}

@pytest.mark.asyncio
async def test_stream_agent_response_sync():
    """Test streaming response from synchronous agent"""
    mock_agent = MockAgent("sync")
    
    chunks = []
    async for chunk in stream_agent_response(mock_agent, "test message", {}):
        chunks.append(chunk)
    
    # Should have start, content chunks, and end
    assert len(chunks) >= 3
    
    # Parse the chunks
    parsed_chunks = []
    for chunk in chunks:
        if chunk.startswith('data: '):
            try:
                data = json.loads(chunk[6:])
                parsed_chunks.append(data)
            except json.JSONDecodeError:
                pass
    
    # Check for start and end events
    types = [chunk.get('type') for chunk in parsed_chunks]
    assert 'start' in types
    assert 'end' in types
    assert 'chunk' in types

@pytest.mark.asyncio
async def test_stream_agent_response_error():
    """Test streaming response when agent raises error"""
    class ErrorAgent:
        def run(self, message, context=None):
            raise ValueError("Test error")
    
    error_agent = ErrorAgent()
    
    chunks = []
    async for chunk in stream_agent_response(error_agent, "test message", {}):
        chunks.append(chunk)
    
    # Should have error chunk
    error_found = False
    for chunk in chunks:
        if chunk.startswith('data: '):
            try:
                data = json.loads(chunk[6:])
                if data.get('type') == 'error':
                    error_found = True
                    assert 'Test error' in data.get('data', '')
            except json.JSONDecodeError:
                pass
    
    assert error_found

def test_mock_agent_sync():
    """Test mock agent synchronous functionality"""
    agent = MockAgent("sync")
    response = agent.run("hello")
    
    assert isinstance(response, str)
    assert "hello" in response
    assert "Mock response" in response

@pytest.mark.asyncio
async def test_mock_agent_async():
    """Test mock agent asynchronous functionality"""
    agent = MockAgent("async")
    
    chunks = []
    async for chunk in agent.run_async("hello"):
        chunks.append(chunk)
    
    assert len(chunks) > 0
    full_response = ''.join(chunks)
    assert "hello" in full_response
    assert "Mock async response" in full_response

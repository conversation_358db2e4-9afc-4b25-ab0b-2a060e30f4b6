<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AgentsUI - Multi-Agent Platform</title>
    
    <!-- Pico.css -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@picocss/pico@1/css/pico.min.css">
    
    <!-- Marked.js for Markdown rendering -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    
    <!-- Custom styles -->
    <style>
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem;
        }
        
        .tabs {
            display: flex;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 1rem;
        }
        
        .tab {
            padding: 0.5rem 1rem;
            cursor: pointer;
            border: none;
            background: none;
            color: var(--color);
        }
        
        .tab.active {
            border-bottom: 2px solid var(--primary);
            color: var(--primary);
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .agent-card {
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .agent-status {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .status-loaded {
            background-color: var(--success);
            color: white;
        }
        
        .status-unloaded {
            background-color: var(--secondary);
            color: white;
        }
        
        .chat-container {
            height: 400px;
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            overflow-y: auto;
            padding: 1rem;
            margin-bottom: 1rem;
            background-color: var(--card-background-color);
        }
        
        .message {
            margin-bottom: 1rem;
            padding: 0.5rem;
            border-radius: 0.25rem;
        }
        
        .message.user {
            background-color: var(--primary);
            color: white;
            margin-left: 2rem;
        }
        
        .message.agent {
            background-color: var(--secondary);
            color: white;
            margin-right: 2rem;
        }
        
        .loading {
            opacity: 0.7;
        }
        
        .kb-namespace {
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .hidden {
            display: none;
        }
        
        .error {
            color: var(--del-color);
            background-color: var(--del-background-color);
            padding: 0.5rem;
            border-radius: 0.25rem;
            margin: 0.5rem 0;
        }
        
        .success {
            color: var(--ins-color);
            background-color: var(--ins-background-color);
            padding: 0.5rem;
            border-radius: 0.25rem;
            margin: 0.5rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>🤖 AgentsUI</h1>
            <p>Multi-Agent Management and Chat Platform</p>
        </header>
        
        <nav class="tabs">
            <button class="tab active" data-component="tab" data-target="agents">Agents</button>
            <button class="tab" data-component="tab" data-target="chat">Chat</button>
            <button class="tab" data-component="tab" data-target="knowledge">Knowledge Base</button>
        </nav>
        
        <!-- Agents Tab -->
        <div id="agents" class="tab-content active">
            <h2>Agent Management</h2>
            <button data-component="refresh-agents" onclick="loadAgents()">🔄 Refresh Agents</button>
            <div id="agents-list"></div>
        </div>
        
        <!-- Chat Tab -->
        <div id="chat" class="tab-content">
            <h2>Chat with Agents</h2>
            
            <div>
                <label for="agent-select">Select Agent:</label>
                <select id="agent-select" data-component="agent-selector">
                    <option value="">Select an agent...</option>
                </select>
            </div>
            
            <div id="chat-container" class="chat-container"></div>
            
            <div>
                <input type="text" id="message-input" placeholder="Type your message..." data-component="message-input">
                <button id="send-button" onclick="sendMessage()" data-component="send-button">Send</button>
            </div>
        </div>
        
        <!-- Knowledge Base Tab -->
        <div id="knowledge" class="tab-content">
            <h2>Knowledge Base Management</h2>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                <div>
                    <h3>Namespaces</h3>
                    <button onclick="loadNamespaces()">🔄 Refresh</button>
                    <div id="namespaces-list"></div>
                    
                    <h4>Create New Namespace</h4>
                    <input type="text" id="namespace-name" placeholder="Namespace name">
                    <input type="text" id="namespace-desc" placeholder="Description (optional)">
                    <button onclick="createNamespace()">Create</button>
                </div>
                
                <div>
                    <h3>Upload Documents</h3>
                    <select id="upload-namespace">
                        <option value="">Select namespace...</option>
                    </select>
                    <input type="file" id="file-input" accept=".md,.txt" data-component="file-upload">
                    <button onclick="uploadDocument()">Upload</button>
                    
                    <h4>Search Knowledge Base</h4>
                    <select id="search-namespace">
                        <option value="">Select namespace...</option>
                    </select>
                    <input type="text" id="search-query" placeholder="Search query">
                    <button onclick="searchKnowledgeBase()">Search</button>
                    <div id="search-results"></div>
                </div>
            </div>
        </div>
        
        <!-- Status Messages -->
        <div id="status-messages"></div>
    </div>
    
    <script src="app.js"></script>
</body>
</html>

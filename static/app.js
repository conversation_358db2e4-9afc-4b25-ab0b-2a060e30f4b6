/**
 * @module static.app
 * @description Frontend JavaScript application for AgentsUI
 */

// Global state
let currentAgent = null;
let eventSource = null;

// Test function to verify script loading
window.testFunction = function() {
    console.log('Script loaded successfully!');
    return 'OK';
};

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    console.log('AgentsUI frontend initialized');

    // Debug: Check if functions are available
    console.log('loadAgents function available:', typeof loadAgents);
    console.log('sendMessage function available:', typeof sendMessage);
    console.log('showStatus function available:', typeof showStatus);

    // Setup tab navigation
    setupTabs();

    // Setup enter key for message input
    const messageInput = document.getElementById('message-input');
    if (messageInput) {
        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
    }

    // Load initial data
    loadAgents();
    // loadNamespaces(); // Disabled for now since K<PERSON> is disabled
});

// Tab Management
function setupTabs() {
    const tabs = document.querySelectorAll('[data-component="tab"]');
    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const target = this.dataset.target;
            switchTab(target);
        });
    });
}

function switchTab(targetId) {
    // Update tab buttons
    document.querySelectorAll('.tab').forEach(tab => {
        tab.classList.remove('active');
    });
    document.querySelector(`[data-target="${targetId}"]`).classList.add('active');
    
    // Update tab content
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    document.getElementById(targetId).classList.add('active');
    
    console.log(`Switched to tab: ${targetId}`);
}

// Agent Management
async function loadAgents() {
    console.log('Loading agents...');
    showStatus('Loading agents...', 'info');
    
    try {
        const response = await fetch('/api/agents/list');
        const agents = await response.json();
        
        displayAgents(agents);
        updateAgentSelector(agents);
        showStatus(`Loaded ${agents.length} agents`, 'success');
        
    } catch (error) {
        console.error('Error loading agents:', error);
        showStatus('Error loading agents: ' + error.message, 'error');
    }
}

function displayAgents(agents) {
    const container = document.getElementById('agents-list');
    
    if (agents.length === 0) {
        container.innerHTML = '<p>No agents found in the agents/ directory.</p>';
        return;
    }
    
    container.innerHTML = agents.map(agent => `
        <div class="agent-card">
            <h3>${agent.name}</h3>
            <span class="agent-status ${agent.loaded ? 'status-loaded' : 'status-unloaded'}">
                ${agent.loaded ? 'Loaded' : 'Unloaded'}
            </span>
            <p><strong>Path:</strong> ${agent.path}</p>
            ${agent.config.description ? `<p><strong>Description:</strong> ${agent.config.description}</p>` : ''}
            ${agent.config.capabilities ? `<p><strong>Capabilities:</strong> ${agent.config.capabilities.join(', ')}</p>` : ''}
            <div>
                <button onclick="loadAgent('${agent.name}')" ${agent.loaded ? 'disabled' : ''}>
                    Load Agent
                </button>
                <button onclick="unloadAgent('${agent.name}')" ${!agent.loaded ? 'disabled' : ''}>
                    Unload Agent
                </button>
            </div>
        </div>
    `).join('');
}

function updateAgentSelector(agents) {
    const selector = document.getElementById('agent-select');
    
    // Update chat agent selector
    selector.innerHTML = '<option value="">Select an agent...</option>';
    agents.filter(agent => agent.loaded).forEach(agent => {
        selector.innerHTML += `<option value="${agent.name}">${agent.name}</option>`;
    });
}

async function loadAgent(agentName) {
    console.log(`Loading agent: ${agentName}`);
    showStatus(`Loading agent: ${agentName}...`, 'info');

    try {
        const response = await fetch(`/api/agents/load/${agentName}`, {
            method: 'POST'
        });
        const result = await response.json();

        if (response.ok) {
            showStatus(`Agent ${agentName} loaded successfully`, 'success');
            loadAgents(); // Refresh the list
        } else {
            showStatus(`Error loading agent: ${result.detail}`, 'error');
        }

    } catch (error) {
        console.error('Error loading agent:', error);
        showStatus('Error loading agent: ' + error.message, 'error');
    }
}

async function unloadAgent(agentName) {
    console.log(`Unloading agent: ${agentName}`);
    showStatus(`Unloading agent: ${agentName}...`, 'info');

    try {
        const response = await fetch(`/api/agents/unload/${agentName}`, {
            method: 'POST'
        });
        const result = await response.json();

        if (response.ok) {
            showStatus(`Agent ${agentName} unloaded successfully`, 'success');
            loadAgents(); // Refresh the list
        } else {
            showStatus(`Error unloading agent: ${result.detail}`, 'error');
        }

    } catch (error) {
        console.error('Error unloading agent:', error);
        showStatus('Error unloading agent: ' + error.message, 'error');
    }
}

// Chat Management
function sendMessage() {
    const agentSelect = document.getElementById('agent-select');
    const messageInput = document.getElementById('message-input');
    const message = messageInput.value.trim();

    if (!agentSelect.value) {
        showStatus('Please select an agent first', 'error');
        return;
    }

    if (!message) {
        showStatus('Please enter a message', 'error');
        return;
    }

    currentAgent = agentSelect.value;

    // Add user message to chat
    addMessageToChat('user', message);

    // Clear input
    messageInput.value = '';

    // Send message to agent
    sendMessageToAgent(currentAgent, message);
}

function addMessageToChat(sender, content) {
    const chatContainer = document.getElementById('chat-container');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}`;

    if (sender === 'agent') {
        // Render markdown for agent messages
        messageDiv.innerHTML = marked.parse(content);
    } else {
        messageDiv.textContent = content;
    }

    chatContainer.appendChild(messageDiv);
    chatContainer.scrollTop = chatContainer.scrollHeight;
}

async function sendMessageToAgent(agentName, message) {
    console.log(`Sending message to ${agentName}: ${message}`);

    try {
        // Close existing event source
        if (eventSource) {
            eventSource.close();
        }

        // Create new event source for streaming response
        const response = await fetch(`/api/chat/send/${agentName}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                message: message,
                context: {}
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        // Handle streaming response
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let agentResponse = '';
        let messageDiv = null;

        while (true) {
            const { done, value } = await reader.read();

            if (done) break;

            const chunk = decoder.decode(value);
            const lines = chunk.split('\n');

            for (const line of lines) {
                if (line.startsWith('data: ')) {
                    try {
                        const data = JSON.parse(line.slice(6));

                        if (data.type === 'start') {
                            // Create agent message container
                            messageDiv = document.createElement('div');
                            messageDiv.className = 'message agent';
                            document.getElementById('chat-container').appendChild(messageDiv);
                        } else if (data.type === 'chunk') {
                            // Append chunk to response
                            agentResponse += data.data;
                            if (messageDiv) {
                                messageDiv.innerHTML = marked.parse(agentResponse);
                            }
                            // Auto-scroll
                            const chatContainer = document.getElementById('chat-container');
                            chatContainer.scrollTop = chatContainer.scrollHeight;
                        } else if (data.type === 'end') {
                            console.log('Agent response completed');
                        } else if (data.type === 'error') {
                            showStatus(`Agent error: ${data.data}`, 'error');
                        }
                    } catch (e) {
                        console.error('Error parsing SSE data:', e);
                    }
                }
            }
        }

    } catch (error) {
        console.error('Error sending message:', error);
        showStatus('Error sending message: ' + error.message, 'error');
        addMessageToChat('agent', `Error: ${error.message}`);
    }
}

// Knowledge Base Management
async function loadNamespaces() {
    console.log('Loading namespaces...');

    try {
        const response = await fetch('/api/kb/namespaces');
        const namespaces = await response.json();

        displayNamespaces(namespaces);
        updateNamespaceSelectors(namespaces);

    } catch (error) {
        console.error('Error loading namespaces:', error);
        showStatus('Error loading namespaces: ' + error.message, 'error');
    }
}

function displayNamespaces(namespaces) {
    const container = document.getElementById('namespaces-list');

    if (namespaces.length === 0) {
        container.innerHTML = '<p>No namespaces found.</p>';
        return;
    }

    container.innerHTML = namespaces.map(ns => `
        <div class="kb-namespace">
            <h4>${ns.name}</h4>
            <p><strong>Documents:</strong> ${ns.count}</p>
            ${ns.metadata.description ? `<p><strong>Description:</strong> ${ns.metadata.description}</p>` : ''}
            <button onclick="deleteNamespace('${ns.name}')" style="background-color: var(--del-color);">
                Delete
            </button>
        </div>
    `).join('');
}

function updateNamespaceSelectors(namespaces) {
    const uploadSelector = document.getElementById('upload-namespace');
    const searchSelector = document.getElementById('search-namespace');

    const options = namespaces.map(ns => `<option value="${ns.name}">${ns.name}</option>`).join('');

    uploadSelector.innerHTML = '<option value="">Select namespace...</option>' + options;
    searchSelector.innerHTML = '<option value="">Select namespace...</option>' + options;
}

async function createNamespace() {
    const nameInput = document.getElementById('namespace-name');
    const descInput = document.getElementById('namespace-desc');

    const name = nameInput.value.trim();
    const description = descInput.value.trim();

    if (!name) {
        showStatus('Please enter a namespace name', 'error');
        return;
    }

    console.log(`Creating namespace: ${name}`);
    showStatus(`Creating namespace: ${name}...`, 'info');

    try {
        const response = await fetch(`/api/kb/namespaces/${name}?description=${encodeURIComponent(description)}`, {
            method: 'POST'
        });
        const result = await response.json();

        if (response.ok) {
            showStatus(`Namespace ${name} created successfully`, 'success');
            nameInput.value = '';
            descInput.value = '';
            loadNamespaces(); // Refresh the list
        } else {
            showStatus(`Error creating namespace: ${result.detail}`, 'error');
        }

    } catch (error) {
        console.error('Error creating namespace:', error);
        showStatus('Error creating namespace: ' + error.message, 'error');
    }
}

async function deleteNamespace(namespaceName) {
    if (!confirm(`Are you sure you want to delete namespace "${namespaceName}"? This action cannot be undone.`)) {
        return;
    }

    console.log(`Deleting namespace: ${namespaceName}`);
    showStatus(`Deleting namespace: ${namespaceName}...`, 'info');

    try {
        const response = await fetch(`/api/kb/namespaces/${namespaceName}`, {
            method: 'DELETE'
        });
        const result = await response.json();

        if (response.ok) {
            showStatus(`Namespace ${namespaceName} deleted successfully`, 'success');
            loadNamespaces(); // Refresh the list
        } else {
            showStatus(`Error deleting namespace: ${result.detail}`, 'error');
        }

    } catch (error) {
        console.error('Error deleting namespace:', error);
        showStatus('Error deleting namespace: ' + error.message, 'error');
    }
}

async function uploadDocument() {
    const namespaceSelect = document.getElementById('upload-namespace');
    const fileInput = document.getElementById('file-input');

    const namespace = namespaceSelect.value;
    const file = fileInput.files[0];

    if (!namespace) {
        showStatus('Please select a namespace', 'error');
        return;
    }

    if (!file) {
        showStatus('Please select a file', 'error');
        return;
    }

    console.log(`Uploading ${file.name} to ${namespace}`);
    showStatus(`Uploading ${file.name}...`, 'info');

    try {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('chunk_size', '1000');

        const response = await fetch(`/api/kb/upload/${namespace}`, {
            method: 'POST',
            body: formData
        });
        const result = await response.json();

        if (response.ok) {
            showStatus(`File uploaded successfully. Created ${result.chunks} chunks.`, 'success');
            fileInput.value = '';
            loadNamespaces(); // Refresh to update document counts
        } else {
            showStatus(`Error uploading file: ${result.detail}`, 'error');
        }

    } catch (error) {
        console.error('Error uploading file:', error);
        showStatus('Error uploading file: ' + error.message, 'error');
    }
}

async function searchKnowledgeBase() {
    const namespaceSelect = document.getElementById('search-namespace');
    const queryInput = document.getElementById('search-query');

    const namespace = namespaceSelect.value;
    const query = queryInput.value.trim();

    if (!namespace) {
        showStatus('Please select a namespace', 'error');
        return;
    }

    if (!query) {
        showStatus('Please enter a search query', 'error');
        return;
    }

    console.log(`Searching ${namespace} for: ${query}`);
    showStatus(`Searching...`, 'info');

    try {
        const response = await fetch(`/api/kb/search/${namespace}?query=${encodeURIComponent(query)}&limit=5`);
        const result = await response.json();

        if (response.ok) {
            displaySearchResults(result);
            showStatus(`Found ${result.results.length} results`, 'success');
        } else {
            showStatus(`Error searching: ${result.detail}`, 'error');
        }

    } catch (error) {
        console.error('Error searching:', error);
        showStatus('Error searching: ' + error.message, 'error');
    }
}

function displaySearchResults(result) {
    const container = document.getElementById('search-results');

    if (result.results.length === 0) {
        container.innerHTML = '<p>No results found.</p>';
        return;
    }

    container.innerHTML = `
        <h4>Search Results for "${result.query}"</h4>
        ${result.results.map((item, index) => `
            <div style="border: 1px solid var(--border-color); padding: 1rem; margin: 0.5rem 0; border-radius: 0.25rem;">
                <h5>Result ${index + 1}</h5>
                <p><strong>Source:</strong> ${item.metadata.source || 'Unknown'}</p>
                <p><strong>Relevance Score:</strong> ${(1 - item.distance).toFixed(3)}</p>
                <div style="background-color: var(--card-background-color); padding: 0.5rem; border-radius: 0.25rem; margin-top: 0.5rem;">
                    ${marked.parse(item.content)}
                </div>
            </div>
        `).join('')}
    `;
}

// Utility Functions
function showStatus(message, type = 'info') {
    const container = document.getElementById('status-messages');
    const statusDiv = document.createElement('div');

    statusDiv.className = type === 'error' ? 'error' : (type === 'success' ? 'success' : 'info');
    statusDiv.textContent = message;

    container.appendChild(statusDiv);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (statusDiv.parentNode) {
            statusDiv.parentNode.removeChild(statusDiv);
        }
    }, 5000);

    console.log(`Status (${type}): ${message}`);
}

// Make functions globally available
window.loadAgents = loadAgents;
window.loadAgent = loadAgent;
window.unloadAgent = unloadAgent;
window.sendMessage = sendMessage;
window.createNamespace = createNamespace;
window.deleteNamespace = deleteNamespace;
window.uploadDocument = uploadDocument;
window.searchKnowledgeBase = searchKnowledgeBase;

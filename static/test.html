<!DOCTYPE html>
<html>
<head>
    <title>AgentsUI Test</title>
</head>
<body>
    <h1>JavaScript Function Test</h1>
    <button onclick="testLoadAgents()">Test loadAgents</button>
    <button onclick="testFunction()">Test Script Loading</button>
    <div id="result"></div>
    
    <script src="/static/app.js"></script>
    <script>
        function testLoadAgents() {
            console.log('Testing loadAgents...');
            if (typeof window.loadAgents === 'function') {
                document.getElementById('result').innerHTML = 'loadAgents function is available!';
                window.loadAgents().then(() => {
                    console.log('loadAgents executed successfully');
                }).catch(err => {
                    console.error('loadAgents error:', err);
                });
            } else {
                document.getElementById('result').innerHTML = 'loadAgents function NOT available!';
            }
        }
    </script>
</body>
</html>

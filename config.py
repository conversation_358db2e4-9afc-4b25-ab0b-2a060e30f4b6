"""
@module config
@description Configuration management for AgentsUI application
"""

import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Settings:
    """Application settings loaded from environment variables"""

    def __init__(self):
        # Basic app settings
        self.debug = os.getenv("DEBUG", "False").lower() == "true"
        self.secret_key = os.getenv("SECRET_KEY", "dev-secret-key")
        self.host = os.getenv("HOST", "0.0.0.0")
        self.port = int(os.getenv("PORT", "8000"))

        # Paths
        self.agents_path = os.getenv("AGENTS_PATH", "agents/")
        self.kb_data_path = os.getenv("KB_DATA_PATH", "kb_data/")
        self.chroma_db_path = os.getenv("CHROMA_DB_PATH", "./chroma_db")

        # Logging
        self.log_level = os.getenv("LOG_LEVEL", "INFO")

        # API Keys
        self.openai_api_key = os.getenv("OPENAI_API_KEY", "")

# Global settings instance
settings = Settings()

# Ensure required directories exist
def ensure_directories():
    """Create required directories if they don't exist"""
    directories = [
        settings.agents_path,
        settings.kb_data_path,
        settings.chroma_db_path,
        "logs",
        "static",
        "tests"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)

# Initialize directories on import
ensure_directories()

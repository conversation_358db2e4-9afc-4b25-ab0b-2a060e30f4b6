"""
@module routers.chat
@description API routes for chat functionality with SSE streaming
"""

from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse
from typing import Dict, Any, AsyncGenerator
import json
import asyncio

from utils.logging_config import get_logger
from routers.agents import get_loaded_agent

router = APIRouter()
logger = get_logger("chat")

@router.post("/send/{agent_name}")
async def send_message(agent_name: str, chat_message: Dict[str, Any]) -> StreamingResponse:
    """Send a message to an agent and stream the response"""
    message = chat_message.get("message", "")
    context = chat_message.get("context", {})

    logger.info(f"Sending message to agent {agent_name}: {message[:50]}...")

    try:
        # Get the loaded agent
        agent = get_loaded_agent(agent_name)
        if not agent:
            logger.error(f"Agent {agent_name} not loaded")
            raise HTTPException(status_code=404, detail=f"Agent {agent_name} not loaded")

        # Check if agent has run method
        if not hasattr(agent, 'run'):
            logger.error(f"Agent {agent_name} does not have run method")
            raise HTTPException(status_code=500, detail=f"Agent {agent_name} does not implement run method")

        # Create streaming response
        return StreamingResponse(
            stream_agent_response(agent, message, context),
            media_type="text/plain"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending message to agent {agent_name}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error sending message: {str(e)}")

async def stream_agent_response(agent, message: str, context: Dict[str, Any]) -> AsyncGenerator[str, None]:
    """Stream agent response using SSE format"""
    try:
        logger.info("Starting agent response stream")
        
        # Send start event
        yield f"data: {json.dumps({'type': 'start', 'data': 'Starting response...'})}\n\n"
        
        # Check if agent.run is async or sync
        import inspect
        if inspect.isasyncgenfunction(agent.run):
            # Async generator function
            async for chunk in agent.run(message, context):
                chunk_data = {
                    "type": "chunk",
                    "data": str(chunk)
                }
                yield f"data: {json.dumps(chunk_data)}\n\n"
                await asyncio.sleep(0.01)  # Small delay for better streaming
        elif asyncio.iscoroutinefunction(agent.run):
            # Async function that returns a value
            response = await agent.run(message, context)
            if isinstance(response, str):
                words = response.split()
                for i, word in enumerate(words):
                    chunk_data = {
                        "type": "chunk",
                        "data": word + (" " if i < len(words) - 1 else "")
                    }
                    yield f"data: {json.dumps(chunk_data)}\n\n"
                    await asyncio.sleep(0.05)
        else:
            # Sync agent - run in thread pool
            response = await asyncio.get_event_loop().run_in_executor(
                None, agent.run, message, context
            )
            
            # If response is a string, send it as chunks
            if isinstance(response, str):
                # Split into words for streaming effect
                words = response.split()
                for i, word in enumerate(words):
                    chunk_data = {
                        "type": "chunk",
                        "data": word + (" " if i < len(words) - 1 else "")
                    }
                    yield f"data: {json.dumps(chunk_data)}\n\n"
                    await asyncio.sleep(0.05)  # Delay between words
            else:
                # Send entire response as one chunk
                chunk_data = {
                    "type": "chunk",
                    "data": str(response)
                }
                yield f"data: {json.dumps(chunk_data)}\n\n"
        
        # Send end event
        yield f"data: {json.dumps({'type': 'end', 'data': 'Response complete'})}\n\n"
        logger.info("Agent response stream completed")
        
    except Exception as e:
        logger.error(f"Error in agent response stream: {str(e)}")
        error_data = {
            "type": "error",
            "data": f"Error: {str(e)}"
        }
        yield f"data: {json.dumps(error_data)}\n\n"

@router.get("/history/{agent_name}")
async def get_chat_history(agent_name: str) -> Dict[str, Any]:
    """Get chat history for an agent (placeholder for future implementation)"""
    logger.info(f"Getting chat history for agent: {agent_name}")
    
    # For now, return empty history
    # In a full implementation, this would retrieve from a database
    return {
        "agent": agent_name,
        "history": [],
        "message": "Chat history not implemented yet"
    }

@router.delete("/history/{agent_name}")
async def clear_chat_history(agent_name: str) -> Dict[str, Any]:
    """Clear chat history for an agent (placeholder for future implementation)"""
    logger.info(f"Clearing chat history for agent: {agent_name}")
    
    # For now, just return success
    # In a full implementation, this would clear from a database
    return {
        "agent": agent_name,
        "status": "cleared",
        "message": "Chat history clearing not implemented yet"
    }

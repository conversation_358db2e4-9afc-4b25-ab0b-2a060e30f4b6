"""
@module routers.kb
@description API routes for knowledge base management functionality
"""

from fastapi import APIRouter, HTTPException, UploadFile, File, Form
from typing import List, Dict, Any
import os
import aiofiles
from pathlib import Path
import chromadb
from chromadb.config import Settings as ChromaSettings

from config import settings
from utils.logging_config import get_logger

router = APIRouter()
logger = get_logger("kb")

# Initialize ChromaDB client
chroma_client = None

def get_chroma_client():
    """Get or create ChromaDB client"""
    global chroma_client
    if chroma_client is None:
        try:
            chroma_client = chromadb.PersistentClient(
                path=settings.chroma_db_path,
                settings=ChromaSettings(anonymized_telemetry=False)
            )
            logger.info(f"ChromaDB client initialized at {settings.chroma_db_path}")
        except Exception as e:
            logger.error(f"Error initializing ChromaDB: {str(e)}")
            raise
    return chroma_client

@router.get("/namespaces")
async def list_namespaces() -> List[Dict[str, Any]]:
    """List all knowledge base namespaces (collections)"""
    logger.info("Listing knowledge base namespaces")
    
    try:
        client = get_chroma_client()
        collections = client.list_collections()
        
        namespaces = []
        for collection in collections:
            namespace_info = {
                "name": collection.name,
                "count": collection.count(),
                "metadata": collection.metadata or {}
            }
            namespaces.append(namespace_info)
        
        logger.info(f"Found {len(namespaces)} namespaces")
        return namespaces
        
    except Exception as e:
        logger.error(f"Error listing namespaces: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error listing namespaces: {str(e)}")

@router.post("/namespaces/{namespace_name}")
async def create_namespace(namespace_name: str, description: str = "") -> Dict[str, Any]:
    """Create a new knowledge base namespace"""
    logger.info(f"Creating namespace: {namespace_name}")
    
    try:
        client = get_chroma_client()
        
        # Check if collection already exists
        try:
            existing = client.get_collection(namespace_name)
            if existing:
                logger.warning(f"Namespace {namespace_name} already exists")
                return {"status": "exists", "namespace": namespace_name}
        except:
            pass  # Collection doesn't exist, which is what we want
        
        # Create new collection
        collection = client.create_collection(
            name=namespace_name,
            metadata={"description": description}
        )
        
        logger.info(f"Successfully created namespace: {namespace_name}")
        return {"status": "created", "namespace": namespace_name}
        
    except Exception as e:
        logger.error(f"Error creating namespace {namespace_name}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error creating namespace: {str(e)}")

@router.delete("/namespaces/{namespace_name}")
async def delete_namespace(namespace_name: str) -> Dict[str, Any]:
    """Delete a knowledge base namespace"""
    logger.info(f"Deleting namespace: {namespace_name}")
    
    try:
        client = get_chroma_client()
        client.delete_collection(namespace_name)
        
        logger.info(f"Successfully deleted namespace: {namespace_name}")
        return {"status": "deleted", "namespace": namespace_name}
        
    except Exception as e:
        logger.error(f"Error deleting namespace {namespace_name}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error deleting namespace: {str(e)}")

@router.post("/upload/{namespace_name}")
async def upload_document(
    namespace_name: str,
    file: UploadFile = File(...),
    chunk_size: int = Form(1000)
) -> Dict[str, Any]:
    """Upload and process a document into the knowledge base"""
    logger.info(f"Uploading document {file.filename} to namespace {namespace_name}")
    
    try:
        # Validate file type
        if not file.filename.endswith(('.md', '.txt')):
            raise HTTPException(status_code=400, detail="Only .md and .txt files are supported")
        
        # Read file content
        content = await file.read()
        text_content = content.decode('utf-8')
        
        # Save file to kb_data directory
        file_path = Path(settings.kb_data_path) / namespace_name
        file_path.mkdir(parents=True, exist_ok=True)
        
        saved_file_path = file_path / file.filename
        async with aiofiles.open(saved_file_path, 'w', encoding='utf-8') as f:
            await f.write(text_content)
        
        # Process and add to ChromaDB
        chunks = chunk_text(text_content, chunk_size)
        await add_chunks_to_collection(namespace_name, chunks, file.filename)
        
        logger.info(f"Successfully uploaded and processed {file.filename}")
        return {
            "status": "uploaded",
            "filename": file.filename,
            "namespace": namespace_name,
            "chunks": len(chunks)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading document: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error uploading document: {str(e)}")

@router.get("/search/{namespace_name}")
async def search_knowledge_base(
    namespace_name: str,
    query: str,
    limit: int = 5
) -> Dict[str, Any]:
    """Search the knowledge base"""
    logger.info(f"Searching namespace {namespace_name} for: {query[:50]}...")
    
    try:
        client = get_chroma_client()
        collection = client.get_collection(namespace_name)
        
        results = collection.query(
            query_texts=[query],
            n_results=limit
        )
        
        search_results = []
        if results['documents'] and results['documents'][0]:
            for i, doc in enumerate(results['documents'][0]):
                result = {
                    "content": doc,
                    "metadata": results['metadatas'][0][i] if results['metadatas'][0] else {},
                    "distance": results['distances'][0][i] if results['distances'][0] else 0
                }
                search_results.append(result)
        
        logger.info(f"Found {len(search_results)} results")
        return {
            "query": query,
            "namespace": namespace_name,
            "results": search_results
        }
        
    except Exception as e:
        logger.error(f"Error searching knowledge base: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error searching: {str(e)}")

def chunk_text(text: str, chunk_size: int = 1000) -> List[str]:
    """Split text into chunks for vector storage"""
    # Simple chunking by character count
    # In a production system, you might want more sophisticated chunking
    chunks = []
    for i in range(0, len(text), chunk_size):
        chunk = text[i:i + chunk_size]
        if chunk.strip():  # Only add non-empty chunks
            chunks.append(chunk.strip())
    return chunks

async def add_chunks_to_collection(namespace_name: str, chunks: List[str], filename: str):
    """Add text chunks to ChromaDB collection"""
    try:
        client = get_chroma_client()
        
        # Get or create collection
        try:
            collection = client.get_collection(namespace_name)
        except:
            collection = client.create_collection(namespace_name)
        
        # Prepare data for insertion
        ids = [f"{filename}_{i}" for i in range(len(chunks))]
        metadatas = [{"source": filename, "chunk_index": i} for i in range(len(chunks))]
        
        # Add to collection
        collection.add(
            documents=chunks,
            metadatas=metadatas,
            ids=ids
        )
        
        logger.info(f"Added {len(chunks)} chunks to collection {namespace_name}")
        
    except Exception as e:
        logger.error(f"Error adding chunks to collection: {str(e)}")
        raise

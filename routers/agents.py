"""
@module routers.agents
@description API routes for agent management functionality
"""

from fastapi import APIRouter, HTTPException
from typing import List, Dict, Any
import os
import importlib.util
from pathlib import Path

from config import settings
from utils.logging_config import get_logger

router = APIRouter()
logger = get_logger("agents")

# Global storage for loaded agents
loaded_agents: Dict[str, Any] = {}

@router.get("/list")
async def list_agents() -> List[Dict[str, Any]]:
    """List all available agents in the agents directory"""
    logger.info("Listing available agents")
    
    try:
        agents_path = Path(settings.agents_path)
        agents = []
        
        if not agents_path.exists():
            logger.warning(f"Agents directory {agents_path} does not exist")
            return agents
        
        for agent_dir in agents_path.iterdir():
            if agent_dir.is_dir() and (agent_dir / "agent.py").exists():
                agent_info = {
                    "name": agent_dir.name,
                    "path": str(agent_dir),
                    "loaded": agent_dir.name in loaded_agents,
                    "config": await get_agent_config(agent_dir.name)
                }
                agents.append(agent_info)
                logger.info(f"Found agent: {agent_dir.name}")
        
        logger.info(f"Listed {len(agents)} agents")
        return agents
        
    except Exception as e:
        logger.error(f"Error listing agents: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error listing agents: {str(e)}")

@router.post("/load/{agent_name}")
async def load_agent(agent_name: str) -> Dict[str, Any]:
    """Load a specific agent"""
    logger.info(f"Loading agent: {agent_name}")
    
    try:
        if agent_name in loaded_agents:
            logger.info(f"Agent {agent_name} already loaded")
            return {"status": "already_loaded", "agent": agent_name}
        
        agent_path = Path(settings.agents_path) / agent_name / "agent.py"
        
        if not agent_path.exists():
            logger.error(f"Agent file not found: {agent_path}")
            raise HTTPException(status_code=404, detail=f"Agent {agent_name} not found")
        
        # Load the agent module
        spec = importlib.util.spec_from_file_location(f"agent_{agent_name}", agent_path)
        agent_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(agent_module)
        
        # Store the loaded agent
        loaded_agents[agent_name] = agent_module
        
        logger.info(f"Successfully loaded agent: {agent_name}")
        return {"status": "loaded", "agent": agent_name}
        
    except Exception as e:
        logger.error(f"Error loading agent {agent_name}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error loading agent: {str(e)}")

@router.post("/unload/{agent_name}")
async def unload_agent(agent_name: str) -> Dict[str, Any]:
    """Unload a specific agent"""
    logger.info(f"Unloading agent: {agent_name}")
    
    try:
        if agent_name not in loaded_agents:
            logger.warning(f"Agent {agent_name} not loaded")
            return {"status": "not_loaded", "agent": agent_name}
        
        # Remove from loaded agents
        del loaded_agents[agent_name]
        
        logger.info(f"Successfully unloaded agent: {agent_name}")
        return {"status": "unloaded", "agent": agent_name}
        
    except Exception as e:
        logger.error(f"Error unloading agent {agent_name}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error unloading agent: {str(e)}")

@router.get("/status/{agent_name}")
async def get_agent_status(agent_name: str) -> Dict[str, Any]:
    """Get the status of a specific agent"""
    logger.info(f"Getting status for agent: {agent_name}")
    
    try:
        agent_path = Path(settings.agents_path) / agent_name
        
        if not agent_path.exists():
            raise HTTPException(status_code=404, detail=f"Agent {agent_name} not found")
        
        status = {
            "name": agent_name,
            "exists": True,
            "loaded": agent_name in loaded_agents,
            "config": await get_agent_config(agent_name)
        }
        
        return status
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting agent status {agent_name}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting agent status: {str(e)}")

async def get_agent_config(agent_name: str) -> Dict[str, Any]:
    """Get configuration for a specific agent"""
    try:
        agent_path = Path(settings.agents_path) / agent_name / "agent.py"
        
        if not agent_path.exists():
            return {}
        
        # Try to load config from the agent module
        spec = importlib.util.spec_from_file_location(f"agent_{agent_name}", agent_path)
        agent_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(agent_module)
        
        if hasattr(agent_module, 'load_config'):
            return agent_module.load_config()
        else:
            return {"description": "No configuration available"}
            
    except Exception as e:
        logger.error(f"Error loading config for agent {agent_name}: {str(e)}")
        return {"error": f"Error loading config: {str(e)}"}

def get_loaded_agent(agent_name: str):
    """Get a loaded agent instance"""
    return loaded_agents.get(agent_name)

2025-08-04 14:38:13 [INFO] agents - Listing available agents
2025-08-04 14:38:13 [INFO] agents - Listing available agents
2025-08-04 14:38:13 [INFO] agents - Found agent: math_agent
2025-08-04 14:38:13 [INFO] agents - Found agent: math_agent
2025-08-04 14:38:13 [INFO] agents - Found agent: example_agent
2025-08-04 14:38:13 [INFO] agents - Found agent: example_agent
2025-08-04 14:38:13 [INFO] agents - Listed 2 agents
2025-08-04 14:38:13 [INFO] agents - Listed 2 agents
2025-08-04 14:38:22 [INFO] agents - Loading agent: example_agent
2025-08-04 14:38:22 [INFO] agents - Loading agent: example_agent
2025-08-04 14:38:22 [INFO] agents - Successfully loaded agent: example_agent
2025-08-04 14:38:22 [INFO] agents - Successfully loaded agent: example_agent
2025-08-04 14:39:50 [INFO] agents - Loading agent: example_agent
2025-08-04 14:39:50 [INFO] agents - Loading agent: example_agent
2025-08-04 14:39:50 [INFO] agents - Successfully loaded agent: example_agent
2025-08-04 14:39:50 [INFO] agents - Successfully loaded agent: example_agent
2025-08-04 14:40:51 [INFO] agents - Loading agent: example_agent
2025-08-04 14:40:51 [INFO] agents - Loading agent: example_agent
2025-08-04 14:40:51 [INFO] agents - Successfully loaded agent: example_agent
2025-08-04 14:40:51 [INFO] agents - Successfully loaded agent: example_agent
2025-08-04 14:40:57 [INFO] agents.example_agent - Example agent processing message: Hello there!...
2025-08-04 14:40:57 [INFO] agents.example_agent - Example agent processing message: Hello there!...
2025-08-04 14:40:58 [INFO] agents.example_agent - Example agent completed processing
2025-08-04 14:40:58 [INFO] agents.example_agent - Example agent completed processing
2025-08-04 14:41:05 [INFO] agents - Loading agent: math_agent
2025-08-04 14:41:05 [INFO] agents - Loading agent: math_agent
2025-08-04 14:41:05 [INFO] agents - Successfully loaded agent: math_agent
2025-08-04 14:41:05 [INFO] agents - Successfully loaded agent: math_agent
2025-08-04 14:41:13 [INFO] agents.math_agent - Math agent processing: What is 2 + 2?...
2025-08-04 14:41:13 [INFO] agents.math_agent - Math agent processing: What is 2 + 2?...
2025-08-04 14:41:13 [INFO] agents.math_agent - Math agent completed calculation
2025-08-04 14:41:13 [INFO] agents.math_agent - Math agent completed calculation
2025-08-04 14:41:21 [INFO] agents - Loading agent: test_agent
2025-08-04 14:41:21 [INFO] agents - Successfully loaded agent: test_agent
2025-08-04 14:41:21 [INFO] agents - Loading agent: nonexistent_agent
2025-08-04 14:41:21 [ERROR] agents - Agent file not found: /tmp/tmpfrd4z_jr/nonexistent_agent/agent.py
2025-08-04 14:41:21 [ERROR] agents - Error loading agent nonexistent_agent: 
2025-08-04 14:41:21 [INFO] agents - Loading agent: test_agent
2025-08-04 14:41:21 [INFO] agents - Agent test_agent already loaded
2025-08-04 14:41:21 [INFO] agents - Loading agent: test_agent
2025-08-04 14:41:21 [INFO] agents - Agent test_agent already loaded
2025-08-04 14:41:21 [INFO] agents - Loading agent: test_agent
2025-08-04 14:41:21 [INFO] agents - Agent test_agent already loaded
2025-08-04 14:41:21 [INFO] agents - Unloading agent: test_agent
2025-08-04 14:41:21 [INFO] agents - Successfully unloaded agent: test_agent
2025-08-04 14:41:21 [INFO] agents - Unloading agent: test_agent
2025-08-04 14:41:21 [WARNING] agents - Agent test_agent not loaded
2025-08-04 14:41:56 [INFO] agents - Listing available agents
2025-08-04 14:41:56 [INFO] agents - Listed 0 agents
2025-08-04 15:11:00 [INFO] agents - Listing available agents
2025-08-04 15:11:00 [INFO] agents - Listing available agents
2025-08-04 15:11:00 [INFO] agents - Found agent: math_agent
2025-08-04 15:11:00 [INFO] agents - Found agent: math_agent
2025-08-04 15:11:00 [INFO] agents - Found agent: example_agent
2025-08-04 15:11:00 [INFO] agents - Found agent: example_agent
2025-08-04 15:11:00 [INFO] agents - Listed 2 agents
2025-08-04 15:11:00 [INFO] agents - Listed 2 agents
2025-08-04 15:11:14 [INFO] agents - Loading agent: math_agent
2025-08-04 15:11:14 [INFO] agents - Loading agent: math_agent
2025-08-04 15:11:14 [INFO] agents - Successfully loaded agent: math_agent
2025-08-04 15:11:14 [INFO] agents - Successfully loaded agent: math_agent
2025-08-04 15:11:14 [INFO] agents - Listing available agents
2025-08-04 15:11:14 [INFO] agents - Listing available agents
2025-08-04 15:11:14 [INFO] agents - Found agent: math_agent
2025-08-04 15:11:14 [INFO] agents - Found agent: math_agent
2025-08-04 15:11:14 [INFO] agents - Found agent: example_agent
2025-08-04 15:11:14 [INFO] agents - Found agent: example_agent
2025-08-04 15:11:14 [INFO] agents - Listed 2 agents
2025-08-04 15:11:14 [INFO] agents - Listed 2 agents
2025-08-04 15:11:23 [INFO] agents - Loading agent: example_agent
2025-08-04 15:11:23 [INFO] agents - Loading agent: example_agent
2025-08-04 15:11:23 [INFO] agents - Successfully loaded agent: example_agent
2025-08-04 15:11:23 [INFO] agents - Successfully loaded agent: example_agent
2025-08-04 15:11:23 [INFO] agents - Listing available agents
2025-08-04 15:11:23 [INFO] agents - Listing available agents
2025-08-04 15:11:23 [INFO] agents - Found agent: math_agent
2025-08-04 15:11:23 [INFO] agents - Found agent: math_agent
2025-08-04 15:11:23 [INFO] agents - Found agent: example_agent
2025-08-04 15:11:23 [INFO] agents - Found agent: example_agent
2025-08-04 15:11:23 [INFO] agents - Listed 2 agents
2025-08-04 15:11:23 [INFO] agents - Listed 2 agents
2025-08-04 15:12:21 [INFO] agents - Listing available agents
2025-08-04 15:12:21 [INFO] agents - Listing available agents
2025-08-04 15:12:21 [INFO] agents - Found agent: math_agent
2025-08-04 15:12:21 [INFO] agents - Found agent: math_agent
2025-08-04 15:12:21 [INFO] agents - Found agent: example_agent
2025-08-04 15:12:21 [INFO] agents - Found agent: example_agent
2025-08-04 15:12:21 [INFO] agents - Listed 2 agents
2025-08-04 15:12:21 [INFO] agents - Listed 2 agents
2025-08-04 15:13:07 [INFO] agents - Unloading agent: math_agent
2025-08-04 15:13:07 [INFO] agents - Unloading agent: math_agent
2025-08-04 15:13:07 [INFO] agents - Successfully unloaded agent: math_agent
2025-08-04 15:13:07 [INFO] agents - Successfully unloaded agent: math_agent
2025-08-04 15:13:07 [INFO] agents - Listing available agents
2025-08-04 15:13:07 [INFO] agents - Listing available agents
2025-08-04 15:13:07 [INFO] agents - Found agent: math_agent
2025-08-04 15:13:07 [INFO] agents - Found agent: math_agent
2025-08-04 15:13:07 [INFO] agents - Found agent: example_agent
2025-08-04 15:13:07 [INFO] agents - Found agent: example_agent
2025-08-04 15:13:07 [INFO] agents - Listed 2 agents
2025-08-04 15:13:07 [INFO] agents - Listed 2 agents
2025-08-04 15:13:08 [INFO] agents - Loading agent: math_agent
2025-08-04 15:13:08 [INFO] agents - Loading agent: math_agent
2025-08-04 15:13:08 [INFO] agents - Successfully loaded agent: math_agent
2025-08-04 15:13:08 [INFO] agents - Successfully loaded agent: math_agent
2025-08-04 15:13:08 [INFO] agents - Listing available agents
2025-08-04 15:13:08 [INFO] agents - Listing available agents
2025-08-04 15:13:08 [INFO] agents - Found agent: math_agent
2025-08-04 15:13:08 [INFO] agents - Found agent: math_agent
2025-08-04 15:13:08 [INFO] agents - Found agent: example_agent
2025-08-04 15:13:08 [INFO] agents - Found agent: example_agent
2025-08-04 15:13:08 [INFO] agents - Listed 2 agents
2025-08-04 15:13:08 [INFO] agents - Listed 2 agents
2025-08-04 15:13:13 [INFO] agents - Listing available agents
2025-08-04 15:13:13 [INFO] agents - Listing available agents
2025-08-04 15:13:13 [INFO] agents - Found agent: math_agent
2025-08-04 15:13:13 [INFO] agents - Found agent: math_agent
2025-08-04 15:13:13 [INFO] agents - Found agent: example_agent
2025-08-04 15:13:13 [INFO] agents - Found agent: example_agent
2025-08-04 15:13:13 [INFO] agents - Listed 2 agents
2025-08-04 15:13:13 [INFO] agents - Listed 2 agents
2025-08-04 15:14:19 [INFO] agents - Loading agent: example_agent
2025-08-04 15:14:19 [INFO] agents - Loading agent: example_agent
2025-08-04 15:14:19 [INFO] agents - Agent example_agent already loaded
2025-08-04 15:14:19 [INFO] agents - Agent example_agent already loaded
2025-08-04 15:14:29 [INFO] agents.example_agent - Example agent processing message: 你好！...
2025-08-04 15:14:29 [INFO] agents.example_agent - Example agent processing message: 你好！...
2025-08-04 15:14:30 [INFO] agents.example_agent - Example agent completed processing
2025-08-04 15:14:30 [INFO] agents.example_agent - Example agent completed processing
2025-08-04 15:15:00 [INFO] agents - Unloading agent: example_agent
2025-08-04 15:15:00 [INFO] agents - Unloading agent: example_agent
2025-08-04 15:15:00 [INFO] agents - Successfully unloaded agent: example_agent
2025-08-04 15:15:00 [INFO] agents - Successfully unloaded agent: example_agent
2025-08-04 15:15:00 [INFO] agents - Listing available agents
2025-08-04 15:15:00 [INFO] agents - Listing available agents
2025-08-04 15:15:00 [INFO] agents - Found agent: math_agent
2025-08-04 15:15:00 [INFO] agents - Found agent: math_agent
2025-08-04 15:15:00 [INFO] agents - Found agent: example_agent
2025-08-04 15:15:00 [INFO] agents - Found agent: example_agent
2025-08-04 15:15:00 [INFO] agents - Listed 2 agents
2025-08-04 15:15:00 [INFO] agents - Listed 2 agents
2025-08-04 15:15:33 [INFO] agents.math_agent - Math agent processing: 11111...
2025-08-04 15:15:33 [INFO] agents.math_agent - Math agent processing: 11111...
2025-08-04 15:15:33 [INFO] agents.math_agent - Math agent completed calculation
2025-08-04 15:15:33 [INFO] agents.math_agent - Math agent completed calculation
2025-08-04 15:15:43 [INFO] agents.math_agent - Math agent processing: 111+11...
2025-08-04 15:15:43 [INFO] agents.math_agent - Math agent processing: 111+11...
2025-08-04 15:15:43 [INFO] agents.math_agent - Math agent completed calculation
2025-08-04 15:15:43 [INFO] agents.math_agent - Math agent completed calculation
2025-08-04 15:16:00 [INFO] agents.math_agent - Math agent processing: 2 + 3 * 4...
2025-08-04 15:16:00 [INFO] agents.math_agent - Math agent processing: 2 + 3 * 4...
2025-08-04 15:16:00 [INFO] agents.math_agent - Math agent completed calculation
2025-08-04 15:16:00 [INFO] agents.math_agent - Math agent completed calculation
2025-08-04 15:16:17 [INFO] agents - Loading agent: example_agent
2025-08-04 15:16:17 [INFO] agents - Loading agent: example_agent
2025-08-04 15:16:17 [INFO] agents - Successfully loaded agent: example_agent
2025-08-04 15:16:17 [INFO] agents - Successfully loaded agent: example_agent
2025-08-04 15:16:17 [INFO] agents - Listing available agents
2025-08-04 15:16:17 [INFO] agents - Listing available agents
2025-08-04 15:16:17 [INFO] agents - Found agent: math_agent
2025-08-04 15:16:17 [INFO] agents - Found agent: math_agent
2025-08-04 15:16:17 [INFO] agents - Found agent: example_agent
2025-08-04 15:16:17 [INFO] agents - Found agent: example_agent
2025-08-04 15:16:17 [INFO] agents - Listed 2 agents
2025-08-04 15:16:17 [INFO] agents - Listed 2 agents
2025-08-04 15:16:34 [INFO] agents.example_agent - Example agent processing message: 成都新易盛...
2025-08-04 15:16:34 [INFO] agents.example_agent - Example agent processing message: 成都新易盛...
2025-08-04 15:16:35 [INFO] agents.example_agent - Example agent completed processing
2025-08-04 15:16:35 [INFO] agents.example_agent - Example agent completed processing
2025-08-04 15:16:45 [INFO] agents.example_agent - Example agent processing message: 1111111...
2025-08-04 15:16:45 [INFO] agents.example_agent - Example agent processing message: 1111111...
2025-08-04 15:16:46 [INFO] agents.example_agent - Example agent completed processing
2025-08-04 15:16:46 [INFO] agents.example_agent - Example agent completed processing
2025-08-04 15:16:51 [INFO] agents.example_agent - Example agent processing message: 111111111111111...
2025-08-04 15:16:51 [INFO] agents.example_agent - Example agent processing message: 111111111111111...
2025-08-04 15:16:52 [INFO] agents.example_agent - Example agent completed processing
2025-08-04 15:16:52 [INFO] agents.example_agent - Example agent completed processing
2025-08-04 15:17:30 [INFO] agents - Listing available agents
2025-08-04 15:17:30 [INFO] agents - Listing available agents
2025-08-04 15:17:30 [INFO] agents - Found agent: math_agent
2025-08-04 15:17:30 [INFO] agents - Found agent: math_agent
2025-08-04 15:17:30 [INFO] agents - Found agent: example_agent
2025-08-04 15:17:30 [INFO] agents - Found agent: example_agent
2025-08-04 15:17:30 [INFO] agents - Listed 2 agents
2025-08-04 15:17:30 [INFO] agents - Listed 2 agents
2025-08-04 15:21:00 [INFO] agents - Listing available agents
2025-08-04 15:21:00 [INFO] agents - Listing available agents
2025-08-04 15:21:00 [INFO] agents - Found agent: math_agent
2025-08-04 15:21:00 [INFO] agents - Found agent: math_agent
2025-08-04 15:21:00 [INFO] agents - Found agent: example_agent
2025-08-04 15:21:00 [INFO] agents - Found agent: example_agent
2025-08-04 15:21:00 [INFO] agents - Listed 2 agents
2025-08-04 15:21:00 [INFO] agents - Listed 2 agents
2025-08-04 15:21:14 [INFO] agents.example_agent - Example agent processing message: 成都新易盛...
2025-08-04 15:21:14 [INFO] agents.example_agent - Example agent processing message: 成都新易盛...
2025-08-04 15:21:15 [INFO] agents.example_agent - Example agent completed processing
2025-08-04 15:21:15 [INFO] agents.example_agent - Example agent completed processing
2025-08-04 15:22:24 [INFO] agents - Listing available agents
2025-08-04 15:22:24 [INFO] agents - Listing available agents
2025-08-04 15:22:24 [INFO] agents - Found agent: math_agent
2025-08-04 15:22:24 [INFO] agents - Found agent: math_agent
2025-08-04 15:22:24 [INFO] agents - Found agent: example_agent
2025-08-04 15:22:24 [INFO] agents - Found agent: example_agent
2025-08-04 15:22:24 [INFO] agents - Listed 2 agents
2025-08-04 15:22:24 [INFO] agents - Listed 2 agents
2025-08-04 15:23:06 [INFO] agents - Listing available agents
2025-08-04 15:23:06 [INFO] agents - Listing available agents
2025-08-04 15:23:06 [INFO] agents - Found agent: math_agent
2025-08-04 15:23:06 [INFO] agents - Found agent: math_agent
2025-08-04 15:23:06 [INFO] agents - Found agent: example_agent
2025-08-04 15:23:06 [INFO] agents - Found agent: example_agent
2025-08-04 15:23:06 [INFO] agents - Listed 2 agents
2025-08-04 15:23:06 [INFO] agents - Listed 2 agents
2025-08-04 15:23:51 [INFO] agents - Listing available agents
2025-08-04 15:23:51 [INFO] agents - Listing available agents
2025-08-04 15:23:51 [INFO] agents - Found agent: math_agent
2025-08-04 15:23:51 [INFO] agents - Found agent: math_agent
2025-08-04 15:23:51 [INFO] agents - Found agent: example_agent
2025-08-04 15:23:51 [INFO] agents - Found agent: example_agent
2025-08-04 15:23:51 [INFO] agents - Listed 2 agents
2025-08-04 15:23:51 [INFO] agents - Listed 2 agents
2025-08-04 15:24:23 [INFO] agents.math_agent - Math agent processing: 2+2=？...
2025-08-04 15:24:23 [INFO] agents.math_agent - Math agent processing: 2+2=？...
2025-08-04 15:24:23 [INFO] agents.math_agent - Math agent completed calculation
2025-08-04 15:24:23 [INFO] agents.math_agent - Math agent completed calculation
2025-08-04 18:26:07 [INFO] agents - Listing available agents
2025-08-04 18:26:07 [INFO] agents - Listing available agents
2025-08-04 18:26:07 [INFO] agents - Found agent: math_agent
2025-08-04 18:26:07 [INFO] agents - Found agent: math_agent
2025-08-04 18:26:07 [INFO] agents - Found agent: example_agent
2025-08-04 18:26:07 [INFO] agents - Found agent: example_agent
2025-08-04 18:26:07 [INFO] agents - Listed 2 agents
2025-08-04 18:26:07 [INFO] agents - Listed 2 agents
2025-08-04 18:26:15 [INFO] agents.math_agent - Math agent processing: 1...
2025-08-04 18:26:15 [INFO] agents.math_agent - Math agent processing: 1...
2025-08-04 18:26:15 [INFO] agents.math_agent - Math agent completed calculation
2025-08-04 18:26:15 [INFO] agents.math_agent - Math agent completed calculation
2025-08-04 18:28:53 [INFO] agents - Listing available agents
2025-08-04 18:28:53 [INFO] agents - Listing available agents
2025-08-04 18:28:53 [INFO] agents - Found agent: math_agent
2025-08-04 18:28:53 [INFO] agents - Found agent: math_agent
2025-08-04 18:28:53 [INFO] agents - Found agent: example_agent
2025-08-04 18:28:53 [INFO] agents - Found agent: example_agent
2025-08-04 18:28:53 [INFO] agents - Listed 2 agents
2025-08-04 18:28:53 [INFO] agents - Listed 2 agents
2025-08-04 18:28:56 [INFO] agents - Unloading agent: math_agent
2025-08-04 18:28:56 [INFO] agents - Unloading agent: math_agent
2025-08-04 18:28:56 [INFO] agents - Successfully unloaded agent: math_agent
2025-08-04 18:28:56 [INFO] agents - Successfully unloaded agent: math_agent
2025-08-04 18:28:56 [INFO] agents - Listing available agents
2025-08-04 18:28:56 [INFO] agents - Listing available agents
2025-08-04 18:28:56 [INFO] agents - Found agent: math_agent
2025-08-04 18:28:56 [INFO] agents - Found agent: math_agent
2025-08-04 18:28:56 [INFO] agents - Found agent: example_agent
2025-08-04 18:28:56 [INFO] agents - Found agent: example_agent
2025-08-04 18:28:56 [INFO] agents - Listed 2 agents
2025-08-04 18:28:56 [INFO] agents - Listed 2 agents
2025-08-04 18:28:58 [INFO] agents - Loading agent: math_agent
2025-08-04 18:28:58 [INFO] agents - Loading agent: math_agent
2025-08-04 18:28:58 [INFO] agents - Successfully loaded agent: math_agent
2025-08-04 18:28:58 [INFO] agents - Successfully loaded agent: math_agent
2025-08-04 18:28:58 [INFO] agents - Listing available agents
2025-08-04 18:28:58 [INFO] agents - Listing available agents
2025-08-04 18:28:58 [INFO] agents - Found agent: math_agent
2025-08-04 18:28:58 [INFO] agents - Found agent: math_agent
2025-08-04 18:28:58 [INFO] agents - Found agent: example_agent
2025-08-04 18:28:58 [INFO] agents - Found agent: example_agent
2025-08-04 18:28:58 [INFO] agents - Listed 2 agents
2025-08-04 18:28:58 [INFO] agents - Listed 2 agents
2025-08-04 18:29:13 [INFO] agents.math_agent - Math agent processing: 1*2...
2025-08-04 18:29:13 [INFO] agents.math_agent - Math agent processing: 1*2...
2025-08-04 18:29:13 [INFO] agents.math_agent - Math agent completed calculation
2025-08-04 18:29:13 [INFO] agents.math_agent - Math agent completed calculation
2025-08-04 18:29:27 [INFO] agents.math_agent - Math agent processing: 1+2=？...
2025-08-04 18:29:27 [INFO] agents.math_agent - Math agent processing: 1+2=？...
2025-08-04 18:29:27 [INFO] agents.math_agent - Math agent completed calculation
2025-08-04 18:29:27 [INFO] agents.math_agent - Math agent completed calculation

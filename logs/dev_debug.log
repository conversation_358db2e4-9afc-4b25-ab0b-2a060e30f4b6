2025-08-04 14:37:44 [INFO] __main__ - Starting server on 0.0.0.0:8000
2025-08-04 14:37:44 [INFO] main - Starting AgentsUI application
2025-08-04 14:37:44 [INFO] main - Debug mode: True
2025-08-04 14:37:44 [INFO] main - Agents path: agents/
2025-08-04 14:37:44 [INFO] main - Knowledge base path: kb_data/
2025-08-04 14:38:06 [INFO] main - Health check requested
2025-08-04 14:38:06 [INFO] watchfiles.main - 2 changes detected
2025-08-04 14:38:13 [INFO] agents - Listing available agents
2025-08-04 14:38:13 [INFO] agents - Found agent: math_agent
2025-08-04 14:38:13 [INFO] agents - Found agent: example_agent
2025-08-04 14:38:13 [INFO] agents - Listed 2 agents
2025-08-04 14:38:13 [INFO] watchfiles.main - 9 changes detected
2025-08-04 14:38:22 [INFO] agents - Loading agent: example_agent
2025-08-04 14:38:22 [INFO] agents - Successfully loaded agent: example_agent
2025-08-04 14:38:22 [INFO] watchfiles.main - 3 changes detected
2025-08-04 14:38:30 [INFO] chat - Sending message to agent example_agent: Hello there!...
2025-08-04 14:38:30 [INFO] chat - Starting agent response stream
2025-08-04 14:38:30 [INFO] chat - Agent response stream completed
2025-08-04 14:38:30 [INFO] watchfiles.main - 3 changes detected
2025-08-04 14:39:05 [INFO] watchfiles.main - 1 change detected
2025-08-04 14:39:05 [INFO] main - Shutting down AgentsUI application
2025-08-04 14:39:05 [INFO] main - Starting AgentsUI application
2025-08-04 14:39:05 [INFO] main - Debug mode: True
2025-08-04 14:39:05 [INFO] main - Agents path: agents/
2025-08-04 14:39:05 [INFO] main - Knowledge base path: kb_data/
2025-08-04 14:39:05 [INFO] watchfiles.main - 2 changes detected
2025-08-04 14:39:06 [INFO] watchfiles.main - 2 changes detected
2025-08-04 14:39:06 [INFO] watchfiles.main - 2 changes detected
2025-08-04 14:39:33 [INFO] __main__ - Starting server on 0.0.0.0:8000
2025-08-04 14:39:33 [INFO] main - Starting AgentsUI application
2025-08-04 14:39:33 [INFO] main - Debug mode: True
2025-08-04 14:39:33 [INFO] main - Agents path: agents/
2025-08-04 14:39:33 [INFO] main - Knowledge base path: kb_data/
2025-08-04 14:39:50 [INFO] agents - Loading agent: example_agent
2025-08-04 14:39:50 [INFO] agents - Successfully loaded agent: example_agent
2025-08-04 14:39:50 [INFO] watchfiles.main - 3 changes detected
2025-08-04 14:39:57 [INFO] chat - Sending message to agent example_agent: Hello there!...
2025-08-04 14:39:57 [INFO] chat - Starting agent response stream
2025-08-04 14:39:57 [INFO] chat - Agent response stream completed
2025-08-04 14:39:57 [INFO] watchfiles.main - 3 changes detected
2025-08-04 14:40:13 [INFO] watchfiles.main - 1 change detected
2025-08-04 14:40:13 [INFO] main - Shutting down AgentsUI application
2025-08-04 14:40:13 [INFO] main - Starting AgentsUI application
2025-08-04 14:40:13 [INFO] main - Debug mode: True
2025-08-04 14:40:13 [INFO] main - Agents path: agents/
2025-08-04 14:40:13 [INFO] main - Knowledge base path: kb_data/
2025-08-04 14:40:13 [INFO] watchfiles.main - 2 changes detected
2025-08-04 14:40:14 [INFO] watchfiles.main - 2 changes detected
2025-08-04 14:40:14 [INFO] watchfiles.main - 2 changes detected
2025-08-04 14:40:32 [INFO] watchfiles.main - 1 change detected
2025-08-04 14:40:32 [INFO] main - Shutting down AgentsUI application
2025-08-04 14:40:32 [INFO] main - Starting AgentsUI application
2025-08-04 14:40:32 [INFO] main - Debug mode: True
2025-08-04 14:40:32 [INFO] main - Agents path: agents/
2025-08-04 14:40:32 [INFO] main - Knowledge base path: kb_data/
2025-08-04 14:40:33 [INFO] watchfiles.main - 4 changes detected
2025-08-04 14:40:33 [INFO] watchfiles.main - 2 changes detected
2025-08-04 14:40:43 [INFO] chat - Sending message to agent example_agent: Hello there!...
2025-08-04 14:40:43 [ERROR] chat - Agent example_agent not loaded
2025-08-04 14:40:43 [INFO] watchfiles.main - 4 changes detected
2025-08-04 14:40:51 [INFO] agents - Loading agent: example_agent
2025-08-04 14:40:51 [INFO] agents - Successfully loaded agent: example_agent
2025-08-04 14:40:51 [INFO] watchfiles.main - 3 changes detected
2025-08-04 14:40:57 [INFO] chat - Sending message to agent example_agent: Hello there!...
2025-08-04 14:40:57 [INFO] chat - Starting agent response stream
2025-08-04 14:40:57 [INFO] agents.example_agent - Example agent processing message: Hello there!...
2025-08-04 14:40:57 [INFO] watchfiles.main - 4 changes detected
2025-08-04 14:40:58 [INFO] agents.example_agent - Example agent completed processing
2025-08-04 14:40:58 [INFO] chat - Agent response stream completed
2025-08-04 14:40:58 [INFO] watchfiles.main - 4 changes detected
2025-08-04 14:41:05 [INFO] agents - Loading agent: math_agent
2025-08-04 14:41:05 [INFO] agents - Successfully loaded agent: math_agent
2025-08-04 14:41:05 [INFO] watchfiles.main - 3 changes detected
2025-08-04 14:41:13 [INFO] chat - Sending message to agent math_agent: What is 2 + 2?...
2025-08-04 14:41:13 [INFO] chat - Starting agent response stream
2025-08-04 14:41:13 [INFO] agents.math_agent - Math agent processing: What is 2 + 2?...
2025-08-04 14:41:13 [INFO] agents.math_agent - Math agent completed calculation
2025-08-04 14:41:13 [INFO] watchfiles.main - 4 changes detected
2025-08-04 14:41:14 [INFO] chat - Agent response stream completed
2025-08-04 14:41:14 [INFO] watchfiles.main - 3 changes detected
2025-08-04 14:41:21 [INFO] agents - Loading agent: test_agent
2025-08-04 14:41:21 [INFO] agents - Successfully loaded agent: test_agent
2025-08-04 14:41:21 [INFO] agents - Loading agent: nonexistent_agent
2025-08-04 14:41:21 [ERROR] agents - Agent file not found: /tmp/tmpfrd4z_jr/nonexistent_agent/agent.py
2025-08-04 14:41:21 [ERROR] agents - Error loading agent nonexistent_agent: 
2025-08-04 14:41:21 [INFO] agents - Loading agent: test_agent
2025-08-04 14:41:21 [INFO] agents - Agent test_agent already loaded
2025-08-04 14:41:21 [INFO] agents - Loading agent: test_agent
2025-08-04 14:41:21 [INFO] agents - Agent test_agent already loaded
2025-08-04 14:41:21 [INFO] agents - Loading agent: test_agent
2025-08-04 14:41:21 [INFO] agents - Agent test_agent already loaded
2025-08-04 14:41:21 [INFO] agents - Unloading agent: test_agent
2025-08-04 14:41:21 [INFO] agents - Successfully unloaded agent: test_agent
2025-08-04 14:41:21 [INFO] agents - Unloading agent: test_agent
2025-08-04 14:41:21 [WARNING] agents - Agent test_agent not loaded
2025-08-04 14:41:22 [INFO] watchfiles.main - 19 changes detected
2025-08-04 14:41:44 [INFO] watchfiles.main - 1 change detected
2025-08-04 14:41:44 [INFO] main - Shutting down AgentsUI application
2025-08-04 14:41:44 [INFO] main - Starting AgentsUI application
2025-08-04 14:41:44 [INFO] main - Debug mode: True
2025-08-04 14:41:44 [INFO] main - Agents path: agents/
2025-08-04 14:41:44 [INFO] main - Knowledge base path: kb_data/
2025-08-04 14:41:44 [INFO] watchfiles.main - 2 changes detected
2025-08-04 14:41:56 [INFO] agents - Listing available agents
2025-08-04 14:41:56 [INFO] agents - Listed 0 agents
2025-08-04 14:41:56 [INFO] watchfiles.main - 8 changes detected
2025-08-04 14:42:02 [INFO] main - Serving main page
2025-08-04 14:42:02 [INFO] watchfiles.main - 2 changes detected
2025-08-04 14:42:50 [INFO] watchfiles.main - 2 changes detected
2025-08-04 14:42:51 [INFO] watchfiles.main - 2 changes detected
2025-08-04 14:42:51 [INFO] watchfiles.main - 2 changes detected
2025-08-04 15:05:33 [INFO] main - Serving main page
2025-08-04 15:05:33 [INFO] watchfiles.main - 2 changes detected
2025-08-04 15:06:12 [INFO] main - Serving main page
2025-08-04 15:06:13 [INFO] watchfiles.main - 2 changes detected
2025-08-04 15:06:13 [INFO] watchfiles.main - 2 changes detected
2025-08-04 15:06:13 [INFO] main - Serving main page
2025-08-04 15:08:14 [INFO] main - Serving main page
2025-08-04 15:08:14 [INFO] watchfiles.main - 2 changes detected

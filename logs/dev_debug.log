2025-08-04 14:37:44 [INFO] __main__ - Starting server on 0.0.0.0:8000
2025-08-04 14:37:44 [INFO] main - Starting AgentsUI application
2025-08-04 14:37:44 [INFO] main - Debug mode: True
2025-08-04 14:37:44 [INFO] main - Agents path: agents/
2025-08-04 14:37:44 [INFO] main - Knowledge base path: kb_data/
2025-08-04 14:38:06 [INFO] main - Health check requested
2025-08-04 14:38:06 [INFO] watchfiles.main - 2 changes detected
2025-08-04 14:38:13 [INFO] agents - Listing available agents
2025-08-04 14:38:13 [INFO] agents - Found agent: math_agent
2025-08-04 14:38:13 [INFO] agents - Found agent: example_agent
2025-08-04 14:38:13 [INFO] agents - Listed 2 agents
2025-08-04 14:38:13 [INFO] watchfiles.main - 9 changes detected
2025-08-04 14:38:22 [INFO] agents - Loading agent: example_agent
2025-08-04 14:38:22 [INFO] agents - Successfully loaded agent: example_agent
2025-08-04 14:38:22 [INFO] watchfiles.main - 3 changes detected
2025-08-04 14:38:30 [INFO] chat - Sending message to agent example_agent: Hello there!...
2025-08-04 14:38:30 [INFO] chat - Starting agent response stream
2025-08-04 14:38:30 [INFO] chat - Agent response stream completed
2025-08-04 14:38:30 [INFO] watchfiles.main - 3 changes detected
2025-08-04 14:39:05 [INFO] watchfiles.main - 1 change detected
2025-08-04 14:39:05 [INFO] main - Shutting down AgentsUI application
2025-08-04 14:39:05 [INFO] main - Starting AgentsUI application
2025-08-04 14:39:05 [INFO] main - Debug mode: True
2025-08-04 14:39:05 [INFO] main - Agents path: agents/
2025-08-04 14:39:05 [INFO] main - Knowledge base path: kb_data/
2025-08-04 14:39:05 [INFO] watchfiles.main - 2 changes detected
2025-08-04 14:39:06 [INFO] watchfiles.main - 2 changes detected
2025-08-04 14:39:06 [INFO] watchfiles.main - 2 changes detected
2025-08-04 14:39:33 [INFO] __main__ - Starting server on 0.0.0.0:8000
2025-08-04 14:39:33 [INFO] main - Starting AgentsUI application
2025-08-04 14:39:33 [INFO] main - Debug mode: True
2025-08-04 14:39:33 [INFO] main - Agents path: agents/
2025-08-04 14:39:33 [INFO] main - Knowledge base path: kb_data/
2025-08-04 14:39:50 [INFO] agents - Loading agent: example_agent
2025-08-04 14:39:50 [INFO] agents - Successfully loaded agent: example_agent
2025-08-04 14:39:50 [INFO] watchfiles.main - 3 changes detected
2025-08-04 14:39:57 [INFO] chat - Sending message to agent example_agent: Hello there!...
2025-08-04 14:39:57 [INFO] chat - Starting agent response stream
2025-08-04 14:39:57 [INFO] chat - Agent response stream completed
2025-08-04 14:39:57 [INFO] watchfiles.main - 3 changes detected
2025-08-04 14:40:13 [INFO] watchfiles.main - 1 change detected
2025-08-04 14:40:13 [INFO] main - Shutting down AgentsUI application
2025-08-04 14:40:13 [INFO] main - Starting AgentsUI application
2025-08-04 14:40:13 [INFO] main - Debug mode: True
2025-08-04 14:40:13 [INFO] main - Agents path: agents/
2025-08-04 14:40:13 [INFO] main - Knowledge base path: kb_data/
2025-08-04 14:40:13 [INFO] watchfiles.main - 2 changes detected
2025-08-04 14:40:14 [INFO] watchfiles.main - 2 changes detected
2025-08-04 14:40:14 [INFO] watchfiles.main - 2 changes detected
2025-08-04 14:40:32 [INFO] watchfiles.main - 1 change detected
2025-08-04 14:40:32 [INFO] main - Shutting down AgentsUI application
2025-08-04 14:40:32 [INFO] main - Starting AgentsUI application
2025-08-04 14:40:32 [INFO] main - Debug mode: True
2025-08-04 14:40:32 [INFO] main - Agents path: agents/
2025-08-04 14:40:32 [INFO] main - Knowledge base path: kb_data/
2025-08-04 14:40:33 [INFO] watchfiles.main - 4 changes detected
2025-08-04 14:40:33 [INFO] watchfiles.main - 2 changes detected
2025-08-04 14:40:43 [INFO] chat - Sending message to agent example_agent: Hello there!...
2025-08-04 14:40:43 [ERROR] chat - Agent example_agent not loaded
2025-08-04 14:40:43 [INFO] watchfiles.main - 4 changes detected
2025-08-04 14:40:51 [INFO] agents - Loading agent: example_agent
2025-08-04 14:40:51 [INFO] agents - Successfully loaded agent: example_agent
2025-08-04 14:40:51 [INFO] watchfiles.main - 3 changes detected
2025-08-04 14:40:57 [INFO] chat - Sending message to agent example_agent: Hello there!...
2025-08-04 14:40:57 [INFO] chat - Starting agent response stream
2025-08-04 14:40:57 [INFO] agents.example_agent - Example agent processing message: Hello there!...
2025-08-04 14:40:57 [INFO] watchfiles.main - 4 changes detected
2025-08-04 14:40:58 [INFO] agents.example_agent - Example agent completed processing
2025-08-04 14:40:58 [INFO] chat - Agent response stream completed
2025-08-04 14:40:58 [INFO] watchfiles.main - 4 changes detected
2025-08-04 14:41:05 [INFO] agents - Loading agent: math_agent
2025-08-04 14:41:05 [INFO] agents - Successfully loaded agent: math_agent
2025-08-04 14:41:05 [INFO] watchfiles.main - 3 changes detected
2025-08-04 14:41:13 [INFO] chat - Sending message to agent math_agent: What is 2 + 2?...
2025-08-04 14:41:13 [INFO] chat - Starting agent response stream
2025-08-04 14:41:13 [INFO] agents.math_agent - Math agent processing: What is 2 + 2?...
2025-08-04 14:41:13 [INFO] agents.math_agent - Math agent completed calculation
2025-08-04 14:41:13 [INFO] watchfiles.main - 4 changes detected
2025-08-04 14:41:14 [INFO] chat - Agent response stream completed
2025-08-04 14:41:14 [INFO] watchfiles.main - 3 changes detected
2025-08-04 14:41:21 [INFO] agents - Loading agent: test_agent
2025-08-04 14:41:21 [INFO] agents - Successfully loaded agent: test_agent
2025-08-04 14:41:21 [INFO] agents - Loading agent: nonexistent_agent
2025-08-04 14:41:21 [ERROR] agents - Agent file not found: /tmp/tmpfrd4z_jr/nonexistent_agent/agent.py
2025-08-04 14:41:21 [ERROR] agents - Error loading agent nonexistent_agent: 
2025-08-04 14:41:21 [INFO] agents - Loading agent: test_agent
2025-08-04 14:41:21 [INFO] agents - Agent test_agent already loaded
2025-08-04 14:41:21 [INFO] agents - Loading agent: test_agent
2025-08-04 14:41:21 [INFO] agents - Agent test_agent already loaded
2025-08-04 14:41:21 [INFO] agents - Loading agent: test_agent
2025-08-04 14:41:21 [INFO] agents - Agent test_agent already loaded
2025-08-04 14:41:21 [INFO] agents - Unloading agent: test_agent
2025-08-04 14:41:21 [INFO] agents - Successfully unloaded agent: test_agent
2025-08-04 14:41:21 [INFO] agents - Unloading agent: test_agent
2025-08-04 14:41:21 [WARNING] agents - Agent test_agent not loaded
2025-08-04 14:41:22 [INFO] watchfiles.main - 19 changes detected
2025-08-04 14:41:44 [INFO] watchfiles.main - 1 change detected
2025-08-04 14:41:44 [INFO] main - Shutting down AgentsUI application
2025-08-04 14:41:44 [INFO] main - Starting AgentsUI application
2025-08-04 14:41:44 [INFO] main - Debug mode: True
2025-08-04 14:41:44 [INFO] main - Agents path: agents/
2025-08-04 14:41:44 [INFO] main - Knowledge base path: kb_data/
2025-08-04 14:41:44 [INFO] watchfiles.main - 2 changes detected
2025-08-04 14:41:56 [INFO] agents - Listing available agents
2025-08-04 14:41:56 [INFO] agents - Listed 0 agents
2025-08-04 14:41:56 [INFO] watchfiles.main - 8 changes detected
2025-08-04 14:42:02 [INFO] main - Serving main page
2025-08-04 14:42:02 [INFO] watchfiles.main - 2 changes detected
2025-08-04 14:42:50 [INFO] watchfiles.main - 2 changes detected
2025-08-04 14:42:51 [INFO] watchfiles.main - 2 changes detected
2025-08-04 14:42:51 [INFO] watchfiles.main - 2 changes detected
2025-08-04 15:05:33 [INFO] main - Serving main page
2025-08-04 15:05:33 [INFO] watchfiles.main - 2 changes detected
2025-08-04 15:06:12 [INFO] main - Serving main page
2025-08-04 15:06:13 [INFO] watchfiles.main - 2 changes detected
2025-08-04 15:06:13 [INFO] watchfiles.main - 2 changes detected
2025-08-04 15:06:13 [INFO] main - Serving main page
2025-08-04 15:08:14 [INFO] main - Serving main page
2025-08-04 15:08:14 [INFO] watchfiles.main - 2 changes detected
2025-08-04 15:10:03 [INFO] watchfiles.main - 1 change detected
2025-08-04 15:10:04 [INFO] watchfiles.main - 2 changes detected
2025-08-04 15:10:04 [INFO] watchfiles.main - 2 changes detected
2025-08-04 15:10:25 [INFO] main - Serving main page
2025-08-04 15:10:26 [INFO] watchfiles.main - 2 changes detected
2025-08-04 15:10:26 [INFO] watchfiles.main - 2 changes detected
2025-08-04 15:10:40 [INFO] watchfiles.main - 1 change detected
2025-08-04 15:10:41 [INFO] watchfiles.main - 2 changes detected
2025-08-04 15:10:41 [INFO] watchfiles.main - 2 changes detected
2025-08-04 15:10:59 [INFO] main - Serving main page
2025-08-04 15:10:59 [INFO] watchfiles.main - 2 changes detected
2025-08-04 15:11:00 [INFO] agents - Listing available agents
2025-08-04 15:11:00 [INFO] agents - Found agent: math_agent
2025-08-04 15:11:00 [INFO] agents - Found agent: example_agent
2025-08-04 15:11:00 [INFO] agents - Listed 2 agents
2025-08-04 15:11:00 [INFO] watchfiles.main - 3 changes detected
2025-08-04 15:11:09 [INFO] watchfiles.main - 1 change detected
2025-08-04 15:11:09 [INFO] watchfiles.main - 2 changes detected
2025-08-04 15:11:14 [INFO] agents - Loading agent: math_agent
2025-08-04 15:11:14 [INFO] agents - Successfully loaded agent: math_agent
2025-08-04 15:11:14 [INFO] agents - Listing available agents
2025-08-04 15:11:14 [INFO] agents - Found agent: math_agent
2025-08-04 15:11:14 [INFO] agents - Found agent: example_agent
2025-08-04 15:11:14 [INFO] agents - Listed 2 agents
2025-08-04 15:11:14 [INFO] watchfiles.main - 3 changes detected
2025-08-04 15:11:23 [INFO] agents - Loading agent: example_agent
2025-08-04 15:11:23 [INFO] agents - Successfully loaded agent: example_agent
2025-08-04 15:11:23 [INFO] agents - Listing available agents
2025-08-04 15:11:23 [INFO] agents - Found agent: math_agent
2025-08-04 15:11:23 [INFO] agents - Found agent: example_agent
2025-08-04 15:11:23 [INFO] agents - Listed 2 agents
2025-08-04 15:11:24 [INFO] watchfiles.main - 3 changes detected
2025-08-04 15:11:24 [INFO] watchfiles.main - 2 changes detected
2025-08-04 15:11:36 [INFO] watchfiles.main - 1 change detected
2025-08-04 15:11:36 [INFO] watchfiles.main - 2 changes detected
2025-08-04 15:12:08 [INFO] watchfiles.main - 2 changes detected
2025-08-04 15:12:08 [INFO] watchfiles.main - 2 changes detected
2025-08-04 15:12:21 [INFO] main - Serving main page
2025-08-04 15:12:21 [INFO] agents - Listing available agents
2025-08-04 15:12:21 [INFO] agents - Found agent: math_agent
2025-08-04 15:12:21 [INFO] agents - Found agent: example_agent
2025-08-04 15:12:21 [INFO] agents - Listed 2 agents
2025-08-04 15:12:21 [INFO] watchfiles.main - 3 changes detected
2025-08-04 15:13:07 [INFO] agents - Unloading agent: math_agent
2025-08-04 15:13:07 [INFO] agents - Successfully unloaded agent: math_agent
2025-08-04 15:13:07 [INFO] agents - Listing available agents
2025-08-04 15:13:07 [INFO] agents - Found agent: math_agent
2025-08-04 15:13:07 [INFO] agents - Found agent: example_agent
2025-08-04 15:13:07 [INFO] agents - Listed 2 agents
2025-08-04 15:13:07 [INFO] watchfiles.main - 3 changes detected
2025-08-04 15:13:08 [INFO] agents - Loading agent: math_agent
2025-08-04 15:13:08 [INFO] agents - Successfully loaded agent: math_agent
2025-08-04 15:13:08 [INFO] agents - Listing available agents
2025-08-04 15:13:08 [INFO] agents - Found agent: math_agent
2025-08-04 15:13:08 [INFO] agents - Found agent: example_agent
2025-08-04 15:13:08 [INFO] agents - Listed 2 agents
2025-08-04 15:13:09 [INFO] watchfiles.main - 3 changes detected
2025-08-04 15:13:09 [INFO] watchfiles.main - 2 changes detected
2025-08-04 15:13:13 [INFO] agents - Listing available agents
2025-08-04 15:13:13 [INFO] agents - Found agent: math_agent
2025-08-04 15:13:13 [INFO] agents - Found agent: example_agent
2025-08-04 15:13:13 [INFO] agents - Listed 2 agents
2025-08-04 15:13:13 [INFO] watchfiles.main - 3 changes detected
2025-08-04 15:14:19 [INFO] agents - Loading agent: example_agent
2025-08-04 15:14:19 [INFO] agents - Agent example_agent already loaded
2025-08-04 15:14:19 [INFO] watchfiles.main - 3 changes detected
2025-08-04 15:14:29 [INFO] chat - Sending message to agent example_agent: 你好！...
2025-08-04 15:14:29 [INFO] chat - Starting agent response stream
2025-08-04 15:14:29 [INFO] agents.example_agent - Example agent processing message: 你好！...
2025-08-04 15:14:30 [INFO] watchfiles.main - 4 changes detected
2025-08-04 15:14:30 [INFO] watchfiles.main - 2 changes detected
2025-08-04 15:14:30 [INFO] agents.example_agent - Example agent completed processing
2025-08-04 15:14:30 [INFO] chat - Agent response stream completed
2025-08-04 15:14:30 [INFO] watchfiles.main - 2 changes detected
2025-08-04 15:15:00 [INFO] agents - Unloading agent: example_agent
2025-08-04 15:15:00 [INFO] agents - Successfully unloaded agent: example_agent
2025-08-04 15:15:00 [INFO] agents - Listing available agents
2025-08-04 15:15:00 [INFO] agents - Found agent: math_agent
2025-08-04 15:15:00 [INFO] agents - Found agent: example_agent
2025-08-04 15:15:00 [INFO] agents - Listed 2 agents
2025-08-04 15:15:01 [INFO] watchfiles.main - 3 changes detected
2025-08-04 15:15:01 [INFO] watchfiles.main - 2 changes detected
2025-08-04 15:15:33 [INFO] chat - Sending message to agent math_agent: 11111...
2025-08-04 15:15:33 [INFO] chat - Starting agent response stream
2025-08-04 15:15:33 [INFO] agents.math_agent - Math agent processing: 11111...
2025-08-04 15:15:33 [INFO] agents.math_agent - Math agent completed calculation
2025-08-04 15:15:33 [INFO] watchfiles.main - 4 changes detected
2025-08-04 15:15:34 [INFO] chat - Agent response stream completed
2025-08-04 15:15:34 [INFO] watchfiles.main - 3 changes detected
2025-08-04 15:15:43 [INFO] chat - Sending message to agent math_agent: 111+11...
2025-08-04 15:15:43 [INFO] chat - Starting agent response stream
2025-08-04 15:15:43 [INFO] agents.math_agent - Math agent processing: 111+11...
2025-08-04 15:15:43 [INFO] agents.math_agent - Math agent completed calculation
2025-08-04 15:15:43 [INFO] watchfiles.main - 4 changes detected
2025-08-04 15:15:43 [INFO] chat - Agent response stream completed
2025-08-04 15:16:00 [INFO] chat - Sending message to agent math_agent: 2 + 3 * 4...
2025-08-04 15:16:00 [INFO] chat - Starting agent response stream
2025-08-04 15:16:00 [INFO] agents.math_agent - Math agent processing: 2 + 3 * 4...
2025-08-04 15:16:00 [INFO] agents.math_agent - Math agent completed calculation
2025-08-04 15:16:01 [INFO] watchfiles.main - 4 changes detected
2025-08-04 15:16:01 [INFO] watchfiles.main - 2 changes detected
2025-08-04 15:16:01 [INFO] chat - Agent response stream completed
2025-08-04 15:16:02 [INFO] watchfiles.main - 1 change detected
2025-08-04 15:16:02 [INFO] watchfiles.main - 2 changes detected
2025-08-04 15:16:17 [INFO] agents - Loading agent: example_agent
2025-08-04 15:16:17 [INFO] agents - Successfully loaded agent: example_agent
2025-08-04 15:16:17 [INFO] agents - Listing available agents
2025-08-04 15:16:17 [INFO] agents - Found agent: math_agent
2025-08-04 15:16:17 [INFO] agents - Found agent: example_agent
2025-08-04 15:16:17 [INFO] agents - Listed 2 agents
2025-08-04 15:16:17 [INFO] watchfiles.main - 3 changes detected
2025-08-04 15:16:34 [INFO] chat - Sending message to agent example_agent: 成都新易盛...
2025-08-04 15:16:34 [INFO] chat - Starting agent response stream
2025-08-04 15:16:34 [INFO] agents.example_agent - Example agent processing message: 成都新易盛...
2025-08-04 15:16:34 [INFO] watchfiles.main - 4 changes detected
2025-08-04 15:16:35 [INFO] agents.example_agent - Example agent completed processing
2025-08-04 15:16:35 [INFO] chat - Agent response stream completed
2025-08-04 15:16:35 [INFO] watchfiles.main - 4 changes detected
2025-08-04 15:16:45 [INFO] chat - Sending message to agent example_agent: 1111111...
2025-08-04 15:16:45 [INFO] chat - Starting agent response stream
2025-08-04 15:16:45 [INFO] agents.example_agent - Example agent processing message: 1111111...
2025-08-04 15:16:45 [INFO] watchfiles.main - 4 changes detected
2025-08-04 15:16:46 [INFO] agents.example_agent - Example agent completed processing
2025-08-04 15:16:46 [INFO] chat - Agent response stream completed
2025-08-04 15:16:46 [INFO] watchfiles.main - 4 changes detected
2025-08-04 15:16:51 [INFO] chat - Sending message to agent example_agent: 111111111111111...
2025-08-04 15:16:51 [INFO] chat - Starting agent response stream
2025-08-04 15:16:51 [INFO] agents.example_agent - Example agent processing message: 111111111111111...
2025-08-04 15:16:51 [INFO] watchfiles.main - 4 changes detected
2025-08-04 15:16:52 [INFO] agents.example_agent - Example agent completed processing
2025-08-04 15:16:52 [INFO] chat - Agent response stream completed
2025-08-04 15:16:52 [INFO] watchfiles.main - 4 changes detected
2025-08-04 15:17:30 [INFO] main - Serving main page
2025-08-04 15:17:30 [INFO] agents - Listing available agents
2025-08-04 15:17:30 [INFO] agents - Found agent: math_agent
2025-08-04 15:17:30 [INFO] agents - Found agent: example_agent
2025-08-04 15:17:30 [INFO] agents - Listed 2 agents
2025-08-04 15:17:30 [INFO] watchfiles.main - 3 changes detected
2025-08-04 15:20:47 [INFO] watchfiles.main - 1 change detected
2025-08-04 15:20:48 [INFO] watchfiles.main - 2 changes detected
2025-08-04 15:20:48 [INFO] watchfiles.main - 2 changes detected
2025-08-04 15:20:59 [INFO] main - Serving main page
2025-08-04 15:20:59 [INFO] watchfiles.main - 2 changes detected
2025-08-04 15:21:00 [INFO] agents - Listing available agents
2025-08-04 15:21:00 [INFO] agents - Found agent: math_agent
2025-08-04 15:21:00 [INFO] agents - Found agent: example_agent
2025-08-04 15:21:00 [INFO] agents - Listed 2 agents
2025-08-04 15:21:00 [INFO] watchfiles.main - 3 changes detected
2025-08-04 15:21:14 [INFO] chat - Sending message to agent example_agent: 成都新易盛...
2025-08-04 15:21:14 [INFO] chat - Starting agent response stream
2025-08-04 15:21:14 [INFO] agents.example_agent - Example agent processing message: 成都新易盛...
2025-08-04 15:21:14 [INFO] watchfiles.main - 4 changes detected
2025-08-04 15:21:15 [INFO] agents.example_agent - Example agent completed processing
2025-08-04 15:21:15 [INFO] chat - Agent response stream completed
2025-08-04 15:21:15 [INFO] watchfiles.main - 4 changes detected
2025-08-04 15:22:24 [INFO] agents - Listing available agents
2025-08-04 15:22:24 [INFO] agents - Found agent: math_agent
2025-08-04 15:22:24 [INFO] agents - Found agent: example_agent
2025-08-04 15:22:24 [INFO] agents - Listed 2 agents
2025-08-04 15:22:24 [INFO] watchfiles.main - 3 changes detected
2025-08-04 15:23:05 [INFO] main - Serving main page
2025-08-04 15:23:05 [INFO] watchfiles.main - 2 changes detected
2025-08-04 15:23:06 [INFO] agents - Listing available agents
2025-08-04 15:23:06 [INFO] agents - Found agent: math_agent
2025-08-04 15:23:06 [INFO] agents - Found agent: example_agent
2025-08-04 15:23:06 [INFO] agents - Listed 2 agents
2025-08-04 15:23:06 [INFO] watchfiles.main - 3 changes detected
2025-08-04 15:23:51 [INFO] agents - Listing available agents
2025-08-04 15:23:51 [INFO] agents - Found agent: math_agent
2025-08-04 15:23:51 [INFO] agents - Found agent: example_agent
2025-08-04 15:23:51 [INFO] agents - Listed 2 agents
2025-08-04 15:23:51 [INFO] watchfiles.main - 3 changes detected
2025-08-04 15:24:23 [INFO] chat - Sending message to agent math_agent: 2+2=？...
2025-08-04 15:24:23 [INFO] chat - Starting agent response stream
2025-08-04 15:24:23 [INFO] agents.math_agent - Math agent processing: 2+2=？...
2025-08-04 15:24:23 [INFO] agents.math_agent - Math agent completed calculation
2025-08-04 15:24:24 [INFO] watchfiles.main - 4 changes detected
2025-08-04 15:24:24 [INFO] watchfiles.main - 2 changes detected
2025-08-04 15:24:24 [INFO] chat - Agent response stream completed
2025-08-04 15:24:24 [INFO] watchfiles.main - 1 change detected
2025-08-04 18:26:07 [INFO] main - Serving main page
2025-08-04 18:26:07 [INFO] agents - Listing available agents
2025-08-04 18:26:07 [INFO] agents - Found agent: math_agent
2025-08-04 18:26:07 [INFO] agents - Found agent: example_agent
2025-08-04 18:26:07 [INFO] agents - Listed 2 agents
2025-08-04 18:26:07 [INFO] watchfiles.main - 3 changes detected
2025-08-04 18:26:15 [INFO] chat - Sending message to agent math_agent: 1...
2025-08-04 18:26:15 [INFO] chat - Starting agent response stream
2025-08-04 18:26:15 [INFO] agents.math_agent - Math agent processing: 1...
2025-08-04 18:26:15 [INFO] agents.math_agent - Math agent completed calculation
2025-08-04 18:26:15 [INFO] watchfiles.main - 4 changes detected
2025-08-04 18:26:16 [INFO] chat - Agent response stream completed
2025-08-04 18:26:16 [INFO] watchfiles.main - 3 changes detected
2025-08-04 18:28:53 [INFO] agents - Listing available agents
2025-08-04 18:28:53 [INFO] agents - Found agent: math_agent
2025-08-04 18:28:53 [INFO] agents - Found agent: example_agent
2025-08-04 18:28:53 [INFO] agents - Listed 2 agents
2025-08-04 18:28:53 [INFO] watchfiles.main - 3 changes detected
2025-08-04 18:28:56 [INFO] agents - Unloading agent: math_agent
2025-08-04 18:28:56 [INFO] agents - Successfully unloaded agent: math_agent
2025-08-04 18:28:56 [INFO] agents - Listing available agents
2025-08-04 18:28:56 [INFO] agents - Found agent: math_agent
2025-08-04 18:28:56 [INFO] agents - Found agent: example_agent
2025-08-04 18:28:56 [INFO] agents - Listed 2 agents
2025-08-04 18:28:56 [INFO] watchfiles.main - 3 changes detected
2025-08-04 18:28:58 [INFO] agents - Loading agent: math_agent
2025-08-04 18:28:58 [INFO] agents - Successfully loaded agent: math_agent
2025-08-04 18:28:58 [INFO] agents - Listing available agents
2025-08-04 18:28:58 [INFO] agents - Found agent: math_agent
2025-08-04 18:28:58 [INFO] agents - Found agent: example_agent
2025-08-04 18:28:58 [INFO] agents - Listed 2 agents
2025-08-04 18:28:58 [INFO] watchfiles.main - 3 changes detected
2025-08-04 18:29:13 [INFO] chat - Sending message to agent math_agent: 1*2...
2025-08-04 18:29:13 [INFO] chat - Starting agent response stream
2025-08-04 18:29:13 [INFO] agents.math_agent - Math agent processing: 1*2...
2025-08-04 18:29:13 [INFO] agents.math_agent - Math agent completed calculation
2025-08-04 18:29:14 [INFO] watchfiles.main - 4 changes detected
2025-08-04 18:29:14 [INFO] chat - Agent response stream completed
2025-08-04 18:29:14 [INFO] watchfiles.main - 2 changes detected
2025-08-04 18:29:14 [INFO] watchfiles.main - 1 change detected
2025-08-04 18:29:27 [INFO] chat - Sending message to agent math_agent: 1+2=？...
2025-08-04 18:29:27 [INFO] chat - Starting agent response stream
2025-08-04 18:29:27 [INFO] agents.math_agent - Math agent processing: 1+2=？...
2025-08-04 18:29:27 [INFO] agents.math_agent - Math agent completed calculation
2025-08-04 18:29:27 [INFO] watchfiles.main - 4 changes detected
2025-08-04 18:29:28 [INFO] chat - Agent response stream completed
2025-08-04 18:29:28 [INFO] watchfiles.main - 3 changes detected
